"""
바이비트 API 간단 테스트 (requests 없이)
"""

import urllib.request
import json
import pandas as pd
from datetime import datetime

def test_bybit_api():
    """바이비트 API 테스트"""
    print("🚀 바이비트 API 테스트")
    print("=" * 50)
    
    try:
        # 바이비트 공개 API 호출 (BTCUSDT 현재가)
        url = "https://api.bybit.com/v5/market/tickers?category=spot&symbol=BTCUSDT"
        
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode())
        
        if data['retCode'] == 0:
            ticker = data['result']['list'][0]
            
            print("💰 BTCUSDT 현재 정보:")
            print(f"현재가: ${float(ticker['lastPrice']):,.2f}")
            print(f"24시간 변동률: {float(ticker['price24hPcnt'])*100:+.2f}%")
            print(f"24시간 거래량: {float(ticker['volume24h']):,.2f} BTC")
            print(f"24시간 거래대금: ${float(ticker['turnover24h']):,.2f}")
            print(f"최고가: ${float(ticker['highPrice24h']):,.2f}")
            print(f"최저가: ${float(ticker['lowPrice24h']):,.2f}")
            
            # 호가창 정보
            print("\n📋 호가창 정보 수집 중...")
            orderbook_url = "https://api.bybit.com/v5/market/orderbook?category=spot&symbol=BTCUSDT&limit=10"
            
            with urllib.request.urlopen(orderbook_url) as response:
                orderbook_data = json.loads(response.read().decode())
            
            if orderbook_data['retCode'] == 0:
                result = orderbook_data['result']
                bids = result['b'][:5]  # 상위 5개 매수 호가
                asks = result['a'][:5]  # 상위 5개 매도 호가
                
                print("\n매수 호가 (상위 5개):")
                for i, bid in enumerate(bids):
                    print(f"  {i+1}. ${float(bid[0]):,.2f} - {float(bid[1]):.4f} BTC")
                
                print("\n매도 호가 (상위 5개):")
                for i, ask in enumerate(asks):
                    print(f"  {i+1}. ${float(ask[0]):,.2f} - {float(ask[1]):.4f} BTC")
                
                # 스프레드 계산
                best_bid = float(bids[0][0])
                best_ask = float(asks[0][0])
                spread = best_ask - best_bid
                spread_pct = (spread / best_bid) * 100
                
                print(f"\n스프레드: ${spread:.2f} ({spread_pct:.3f}%)")
                
                # 매수/매도 벽 분석
                total_bid_volume = sum([float(bid[1]) for bid in bids])
                total_ask_volume = sum([float(ask[1]) for ask in asks])
                
                print(f"매수 벽 (상위 5단계): {total_bid_volume:.4f} BTC")
                print(f"매도 벽 (상위 5단계): {total_ask_volume:.4f} BTC")
                print(f"매수/매도 비율: {total_bid_volume/total_ask_volume:.2f}")
            
            # 최근 거래 내역
            print("\n💹 최근 거래 내역 수집 중...")
            trades_url = "https://api.bybit.com/v5/market/recent-trade?category=spot&symbol=BTCUSDT&limit=20"
            
            with urllib.request.urlopen(trades_url) as response:
                trades_data = json.loads(response.read().decode())
            
            if trades_data['retCode'] == 0:
                trades = trades_data['result']['list']
                
                buy_volume = 0
                sell_volume = 0
                large_trades = 0
                
                print("\n최근 거래 (상위 10개):")
                for i, trade in enumerate(trades[:10]):
                    price = float(trade['price'])
                    size = float(trade['size'])
                    side = trade['side']
                    timestamp = datetime.fromtimestamp(int(trade['time'])/1000)
                    
                    if side == 'Buy':
                        buy_volume += size
                        side_emoji = "🟢"
                    else:
                        sell_volume += size
                        side_emoji = "🔴"
                    
                    if size > 1:  # 1 BTC 이상을 대량 거래로 간주
                        large_trades += 1
                    
                    print(f"  {i+1}. {side_emoji} ${price:,.2f} - {size:.4f} BTC ({timestamp.strftime('%H:%M:%S')})")
                
                print(f"\n거래 분석 (최근 20건):")
                print(f"매수 거래량: {buy_volume:.4f} BTC")
                print(f"매도 거래량: {sell_volume:.4f} BTC")
                if sell_volume > 0:
                    print(f"매수/매도 비율: {buy_volume/sell_volume:.2f}")
                print(f"대량 거래 (1 BTC 이상): {large_trades}건")
                
                if buy_volume > sell_volume:
                    print("  → 매수 압력 우세")
                elif sell_volume > buy_volume:
                    print("  → 매도 압력 우세")
                else:
                    print("  → 균형 상태")
            
            print(f"\n{'='*50}")
            print("✅ 바이비트 API 테스트 완료!")
            print("실제 키움증권 + 바이비트 통합 시스템 사용 시")
            print("더 상세한 분석과 다양한 종목 지원이 가능합니다.")
            
        else:
            print(f"❌ API 오류: {data['retMsg']}")
            
    except Exception as e:
        print(f"❌ 연결 실패: {e}")
        print("인터넷 연결을 확인해주세요.")

def show_supported_symbols():
    """지원 암호화폐 목록"""
    print("\n📊 바이비트 지원 주요 암호화폐:")
    symbols = [
        ('BTCUSDT', '비트코인'),
        ('ETHUSDT', '이더리움'),
        ('ADAUSDT', '카르다노'),
        ('SOLUSDT', '솔라나'),
        ('DOTUSDT', '폴카닷'),
        ('LINKUSDT', '체인링크'),
        ('MATICUSDT', '폴리곤'),
        ('AVAXUSDT', '아발란체'),
        ('ATOMUSDT', '코스모스'),
        ('NEARUSDT', '니어프로토콜')
    ]
    
    for symbol, name in symbols:
        print(f"  {symbol} - {name}")

def show_korean_stocks():
    """키움증권 지원 주요 한국 주식"""
    print("\n📈 키움증권 지원 주요 한국 주식:")
    stocks = [
        ('005930', '삼성전자'),
        ('000660', 'SK하이닉스'),
        ('035420', 'NAVER'),
        ('051910', 'LG화학'),
        ('006400', '삼성SDI'),
        ('207940', '삼성바이오로직스'),
        ('068270', '셀트리온'),
        ('035720', '카카오'),
        ('003670', '포스코홀딩스'),
        ('096770', 'SK이노베이션')
    ]
    
    for code, name in stocks:
        print(f"  {code} - {name}")

def main():
    """메인 함수"""
    print("🔍 키움증권 + 바이비트 API 연동 시스템 미리보기")
    print("=" * 60)
    
    # 바이비트 API 테스트
    test_bybit_api()
    
    # 지원 종목 안내
    show_supported_symbols()
    show_korean_stocks()
    
    print(f"\n{'='*60}")
    print("📋 다음 단계:")
    print("1. 키움증권 OpenAPI+ 신청 및 API 키 발급")
    print("2. 바이비트 계정 생성 (선택사항 - 공개 데이터는 키 불필요)")
    print("3. kiwoom_bybit_analyzer.py 실행하여 전체 기능 사용")
    print("4. api_setup_guide.md 참고하여 상세 설정")

if __name__ == "__main__":
    main()
