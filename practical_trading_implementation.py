"""
실제 매매를 위한 LSTM 기반 자동화 전략 구현
Practical Trading Implementation using LSTM Predictions
"""

import numpy as np
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class LSTMTradingBot:
    def __init__(self, initial_capital=100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}
        self.trade_history = []
        self.risk_params = self.setup_risk_parameters()
        
    def setup_risk_parameters(self):
        """리스크 관리 파라미터 설정"""
        return {
            'max_position_size': 0.2,      # 최대 포지션 크기 (20%)
            'stop_loss_pct': 0.03,         # 손절매 (3%)
            'take_profit_pct': 0.08,       # 익절 (8%)
            'max_daily_loss': 0.02,        # 일일 최대 손실 (2%)
            'confidence_threshold': 0.6,    # 신뢰도 임계값
            'volatility_limit': 0.25,      # 변동성 제한
        }
    
    def calculate_position_size(self, price, confidence, volatility):
        """
        포지션 크기 계산 (Kelly Criterion 변형)
        """
        # 기본 포지션 크기
        base_size = self.risk_params['max_position_size']
        
        # 신뢰도 기반 조정
        confidence_adj = confidence / self.risk_params['confidence_threshold']
        
        # 변동성 기반 조정 (변동성이 높을수록 포지션 축소)
        volatility_adj = max(0.1, 1 - volatility / self.risk_params['volatility_limit'])
        
        # 최종 포지션 크기
        position_size = base_size * confidence_adj * volatility_adj
        
        # 자본 기준 주식 수 계산
        max_shares = int((self.current_capital * position_size) / price)
        
        return min(max_shares, int(self.current_capital * 0.95 / price))
    
    def generate_trading_signal(self, prediction, current_price, historical_data):
        """
        매매 신호 생성
        
        Args:
            prediction: LSTM 모델 예측값
            current_price: 현재 가격
            historical_data: 과거 데이터 (기술적 분석용)
        
        Returns:
            signal: 'BUY', 'SELL', 'HOLD'
            confidence: 신뢰도 (0-1)
            target_price: 목표 가격
        """
        # 예측 수익률 계산
        predicted_return = (prediction - current_price) / current_price
        
        # 기술적 지표 확인
        rsi = historical_data['RSI'].iloc[-1]
        bb_position = self.calculate_bollinger_position(historical_data)
        volume_ratio = self.calculate_volume_ratio(historical_data)
        
        # 신호 강도 계산
        signal_strength = abs(predicted_return)
        
        # 다중 조건 확인
        conditions = {
            'prediction_bullish': predicted_return > 0.02,
            'prediction_bearish': predicted_return < -0.02,
            'rsi_oversold': rsi < 30,
            'rsi_overbought': rsi > 70,
            'bb_lower': bb_position < 0.2,
            'bb_upper': bb_position > 0.8,
            'high_volume': volume_ratio > 1.5
        }
        
        # 매수 신호
        if (conditions['prediction_bullish'] and 
            (conditions['rsi_oversold'] or conditions['bb_lower']) and
            conditions['high_volume']):
            return 'BUY', min(0.9, signal_strength * 2), prediction
            
        # 매도 신호
        elif (conditions['prediction_bearish'] and 
              (conditions['rsi_overbought'] or conditions['bb_upper'])):
            return 'SELL', min(0.9, signal_strength * 2), prediction
            
        else:
            return 'HOLD', 0.3, current_price
    
    def calculate_bollinger_position(self, data, period=20):
        """볼린저 밴드 내 위치 계산"""
        close = data['Close'].iloc[-period:]
        sma = close.mean()
        std = close.std()
        
        upper_band = sma + (2 * std)
        lower_band = sma - (2 * std)
        current_price = data['Close'].iloc[-1]
        
        return (current_price - lower_band) / (upper_band - lower_band)
    
    def calculate_volume_ratio(self, data, period=20):
        """거래량 비율 계산"""
        volume_avg = data['Volume'].iloc[-period:].mean()
        current_volume = data['Volume'].iloc[-1]
        
        return current_volume / volume_avg if volume_avg > 0 else 1
    
    def execute_trade(self, symbol, signal, shares, price, confidence):
        """거래 실행"""
        timestamp = datetime.now()
        
        if signal == 'BUY':
            cost = shares * price
            if cost <= self.current_capital:
                self.current_capital -= cost
                self.positions[symbol] = {
                    'shares': shares,
                    'entry_price': price,
                    'entry_time': timestamp,
                    'stop_loss': price * (1 - self.risk_params['stop_loss_pct']),
                    'take_profit': price * (1 + self.risk_params['take_profit_pct'])
                }
                
                self.trade_history.append({
                    'timestamp': timestamp,
                    'symbol': symbol,
                    'action': 'BUY',
                    'shares': shares,
                    'price': price,
                    'confidence': confidence,
                    'capital_after': self.current_capital
                })
                
                return True
                
        elif signal == 'SELL' and symbol in self.positions:
            position = self.positions[symbol]
            proceeds = position['shares'] * price
            self.current_capital += proceeds
            
            # 수익률 계산
            profit_loss = (price - position['entry_price']) / position['entry_price']
            
            self.trade_history.append({
                'timestamp': timestamp,
                'symbol': symbol,
                'action': 'SELL',
                'shares': position['shares'],
                'price': price,
                'confidence': confidence,
                'profit_loss': profit_loss,
                'capital_after': self.current_capital
            })
            
            del self.positions[symbol]
            return True
            
        return False
    
    def check_stop_loss_take_profit(self, symbol, current_price):
        """손절매/익절 확인"""
        if symbol not in self.positions:
            return None
            
        position = self.positions[symbol]
        
        if current_price <= position['stop_loss']:
            return 'STOP_LOSS'
        elif current_price >= position['take_profit']:
            return 'TAKE_PROFIT'
            
        return None
    
    def get_portfolio_status(self):
        """포트폴리오 현황 조회"""
        total_value = self.current_capital
        
        for symbol, position in self.positions.items():
            # 현재 가격 조회 (실제 구현시 실시간 데이터 필요)
            current_price = self.get_current_price(symbol)
            position_value = position['shares'] * current_price
            total_value += position_value
        
        return {
            'total_value': total_value,
            'cash': self.current_capital,
            'positions': self.positions,
            'total_return': (total_value - self.initial_capital) / self.initial_capital,
            'num_trades': len(self.trade_history)
        }
    
    def get_current_price(self, symbol):
        """현재 가격 조회 (실제 구현시 실시간 API 사용)"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period="1d", interval="1m")
            return data['Close'].iloc[-1] if not data.empty else 100
        except:
            return 100  # 기본값

class MarketDataManager:
    """시장 데이터 관리 클래스"""
    
    @staticmethod
    def get_real_time_data(symbol, period="1d"):
        """실시간 데이터 조회"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            return data
        except Exception as e:
            print(f"데이터 조회 오류: {e}")
            return None
    
    @staticmethod
    def prepare_lstm_input(data, lookback=30):
        """LSTM 입력 데이터 준비"""
        # 기술적 지표 계산
        data = MarketDataManager.add_technical_indicators(data)
        
        # 정규화 (실제 구현시 학습시 사용한 scaler 사용)
        features = ['Close', 'High', 'Low', 'Open', 'RSI', 'EMAF', 'EMAM', 'EMAS']
        
        # 최근 lookback 기간 데이터 반환
        return data[features].tail(lookback).values
    
    @staticmethod
    def add_technical_indicators(data):
        """기술적 지표 추가"""
        # RSI
        delta = data['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=15).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=15).mean()
        rs = gain / loss
        data['RSI'] = 100 - (100 / (1 + rs))
        
        # EMA
        data['EMAF'] = data['Close'].ewm(span=20).mean()
        data['EMAM'] = data['Close'].ewm(span=100).mean()
        data['EMAS'] = data['Close'].ewm(span=150).mean()
        
        return data

def main_trading_loop():
    """메인 트레이딩 루프"""
    bot = LSTMTradingBot(initial_capital=100000)
    symbol = "QQQ"
    
    print("LSTM 기반 자동 매매 시스템 시작")
    print("=" * 50)
    
    while True:
        try:
            # 1. 시장 데이터 조회
            data = MarketDataManager.get_real_time_data(symbol, period="60d")
            if data is None or data.empty:
                print("데이터 조회 실패")
                continue
            
            # 2. LSTM 입력 데이터 준비
            lstm_input = MarketDataManager.prepare_lstm_input(data)
            
            # 3. 모델 예측 (실제 구현시 학습된 모델 사용)
            # prediction = model.predict(lstm_input)
            current_price = data['Close'].iloc[-1]
            prediction = current_price * (1 + np.random.normal(0, 0.02))  # 임시 예측값
            
            # 4. 매매 신호 생성
            signal, confidence, target_price = bot.generate_trading_signal(
                prediction, current_price, data
            )
            
            # 5. 리스크 관리 확인
            risk_check = bot.check_stop_loss_take_profit(symbol, current_price)
            if risk_check:
                bot.execute_trade(symbol, 'SELL', 0, current_price, 1.0)
                print(f"{risk_check} 실행: {current_price:.2f}")
            
            # 6. 새로운 거래 실행
            if signal in ['BUY', 'SELL'] and confidence > bot.risk_params['confidence_threshold']:
                shares = bot.calculate_position_size(current_price, confidence, 0.2)
                if bot.execute_trade(symbol, signal, shares, current_price, confidence):
                    print(f"{signal} 실행: {shares}주 @ {current_price:.2f} (신뢰도: {confidence:.2f})")
            
            # 7. 포트폴리오 현황 출력
            status = bot.get_portfolio_status()
            print(f"포트폴리오 가치: ${status['total_value']:,.2f} "
                  f"(수익률: {status['total_return']:.2%})")
            
            # 실제 구현시에는 적절한 대기 시간 설정
            # time.sleep(300)  # 5분 대기
            break  # 예시를 위해 한 번만 실행
            
        except KeyboardInterrupt:
            print("매매 시스템 종료")
            break
        except Exception as e:
            print(f"오류 발생: {e}")
            continue

if __name__ == "__main__":
    main_trading_loop()
