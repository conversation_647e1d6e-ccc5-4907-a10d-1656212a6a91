# Install required library
!pip install pandas-ta

# Importing Libraries
import numpy as np
import pandas as pd
import yfinance as yf
import pandas_ta as ta
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from keras.models import Model
from keras.layers import LSTM, Dense, Activation, Input
from keras import optimizers

# Downloading Dataset
data = yf.download(tickers='QQQ', start='2015-01-25', end='2025-01-25')

# Flatten MultiIndex columns
data.columns = data.columns.get_level_values(0)

# Display first 5 rows
data.head()

# Adding Indicators
data['RSI'] = ta.rsi(data['Close'], length=15)
data['EMAF'] = ta.ema(data['Close'], length=20)
data['EMAM'] = ta.ema(data['Close'], length=100)
data['EMAS'] = ta.ema(data['Close'], length=150)

# Create Targets
data['Target'] = data['Close'] - data['Open']
data['Target'] = data['Target'].shift(-1)  # Predict next day's movement
data['TargetClass'] = [1 if data['Target'][i] > 0 else 0 for i in range(len(data))]
data['TargetNextClose'] = data['Close'].shift(-1)

# Clean Missing Data
data.dropna(inplace=True)
data.reset_index(inplace=True)
data.drop(['Volume', 'Date'], axis=1, inplace=True)

# View Dataset
data.head()

# Normalize Dataset
scaler = MinMaxScaler(feature_range=(0, 1))
data_scaled = scaler.fit_transform(data)

# Prepare Input for LSTM
backcandles = 30
X, y = [], []
for i in range(backcandles, len(data_scaled)):
    X.append(data_scaled[i-backcandles:i, :8])  # 8 features for predictors
    y.append(data_scaled[i, -1])  # Target (Next Close Price)
X, y = np.array(X), np.array(y)

# Split Dataset
split_limit = int(len(X) * 0.8)
X_train, X_test = X[:split_limit], X[split_limit:]
y_train, y_test = y[:split_limit], y[split_limit:]

print(f"Training Data Shape: {X_train.shape}, {y_train.shape}")
print(f"Testing Data Shape: {X_test.shape}, {y_test.shape}")

# Building the LSTM Model
lstm_input = Input(shape=(backcandles, 8), name='lstm_input')
lstm_layer = LSTM(150, name='lstm_layer')(lstm_input)
dense_layer = Dense(1, name='dense_layer')(lstm_layer)
output = Activation('linear', name='output')(dense_layer)

model = Model(inputs=lstm_input, outputs=output)
model.compile(optimizer=optimizers.Adam(), loss='mse')

# Training the Model
model.fit(X_train, y_train, batch_size=15, epochs=30, shuffle=True, validation_split=0.1)

# Predict on Test Set
y_pred = model.predict(X_test)

# Compare Predictions with Actual Values
plt.figure(figsize=(16, 8))
plt.plot(y_test, color='black', label='Actual')
plt.plot(y_pred, color='green', label='Predicted')
plt.title("Predicted vs. Actual Closing Prices")
plt.legend()
plt.show()

# Analyze Prediction Differences
diff = y_test - y_pred.flatten()
plt.figure(figsize=(16, 8))
plt.plot(diff, color='red', label='Difference')
plt.title("Prediction Differences")
plt.legend()
plt.show()