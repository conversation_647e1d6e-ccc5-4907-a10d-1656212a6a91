{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# **Stock Price Prediction Using LSTM**\n", "This project predicts the movement in stock price using historical data from the NASDAQ-100 ETF (`QQQ`) through Yahoo Finance. The project utilizes technical indicators, normalization, and an LSTM-based neural network for regression.\n", "\n", "---\n", "## **1. Importing Libraries and Dataset**\n", "We first import all necessary libraries, download the dataset, and then some preprocessing.\n"], "metadata": {"id": "I-xsPV-CyJK-"}}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6k4ObMADx82g", "outputId": "06116672-ccd0-4b6d-ed6b-ff1d95e97bcf"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: pandas-ta in /usr/local/lib/python3.11/dist-packages (0.3.14b0)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from pandas-ta) (2.2.2)\n", "Requirement already satisfied: numpy>=1.23.2 in /usr/local/lib/python3.11/dist-packages (from pandas->pandas-ta) (1.26.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->pandas-ta) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->pandas-ta) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->pandas-ta) (2025.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->pandas-ta) (1.17.0)\n"]}], "source": ["# Install required library\n", "!pip install pandas-ta\n", "\n", "# Importing Libraries\n", "import numpy as np\n", "import pandas as pd\n", "import yfinance as yf\n", "import pandas_ta as ta\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import MinMaxScaler\n", "from keras.models import Model\n", "from keras.layers import LSTM, Dense, Activation, Input\n", "from keras import optimizers"]}, {"cell_type": "markdown", "source": ["About the Dataset\n", "- **Ticker**: `QQQ` (NASDAQ-100 ETF)\n", "- **Time Period**: January 25, 2015, to January 25, 2025 (10 years but can be used until the day before the date of today)\n", "- **Indicators**:\n", "  - **RSI (Relative Strength Index)**: Momentum indicator. It measures overbought or oversold conditions.\n", "  - **EMA (Exponential Moving Average)**: This tracks the trend of prices, giving more importance to the more recent data.\n", "    - `EMAF`: Fast EMA (20-period)\n", "- `EMAM`: EMA of 100 periods (medium)\n", "   - `EMAS`: 150-periods EMA(slow)\n"], "metadata": {"id": "7sRAXPsiyXzZ"}}, {"cell_type": "code", "source": ["# Downloading Dataset\n", "data = yf.download(tickers='QQQ', start='2015-01-25', end='2025-01-25')\n", "\n", "# Flatten MultiIndex columns\n", "data.columns = data.columns.get_level_values(0)\n", "\n", "# Display first 5 rows\n", "data.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 252}, "id": "oNFbxdTwyXZ6", "outputId": "ee71aac3-60ab-4cd7-a97f-7ec9fe8dc2d8"}, "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["\r[*********************100%***********************]  1 of 1 completed\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["Price           Close       High        Low       Open    Volume\n", "Date                                                            \n", "2015-01-26  96.375679  96.551516  95.885195  96.375679  19960900\n", "2015-01-27  93.877007  94.913499  93.562352  94.857975  45253200\n", "2015-01-28  93.395744  95.487248  93.377239  95.385449  43591700\n", "2015-01-29  94.293434  94.469271  92.507328  93.321716  46539700\n", "2015-01-30  93.562332  94.931992  93.432771  94.210147  42927600"], "text/html": ["\n", "  <div id=\"df-445008b8-e64c-4506-b764-d0ff563af27d\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-26</th>\n", "      <td>96.375679</td>\n", "      <td>96.551516</td>\n", "      <td>95.885195</td>\n", "      <td>96.375679</td>\n", "      <td>19960900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-27</th>\n", "      <td>93.877007</td>\n", "      <td>94.913499</td>\n", "      <td>93.562352</td>\n", "      <td>94.857975</td>\n", "      <td>45253200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-28</th>\n", "      <td>93.395744</td>\n", "      <td>95.487248</td>\n", "      <td>93.377239</td>\n", "      <td>95.385449</td>\n", "      <td>43591700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-29</th>\n", "      <td>94.293434</td>\n", "      <td>94.469271</td>\n", "      <td>92.507328</td>\n", "      <td>93.321716</td>\n", "      <td>46539700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-30</th>\n", "      <td>93.562332</td>\n", "      <td>94.931992</td>\n", "      <td>93.432771</td>\n", "      <td>94.210147</td>\n", "      <td>42927600</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-445008b8-e64c-4506-b764-d0ff563af27d')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-445008b8-e64c-4506-b764-d0ff563af27d button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-445008b8-e64c-4506-b764-d0ff563af27d');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-f343ae9b-4528-4969-833c-13379b695e1b\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-f343ae9b-4528-4969-833c-13379b695e1b')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-f343ae9b-4528-4969-833c-13379b695e1b button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "data", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 2516,\n  \"fields\": [\n    {\n      \"column\": \"Date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2015-01-26 00:00:00\",\n        \"max\": \"2025-01-24 00:00:00\",\n        \"num_unique_values\": 2516,\n        \"samples\": [\n          \"2017-07-07 00:00:00\",\n          \"2018-09-28 00:00:00\",\n          \"2018-10-19 00:00:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Close\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 119.90981686718868,\n        \"min\": 90.04122924804688,\n        \"max\": 537.3035888671875,\n        \"num_unique_values\": 2481,\n        \"samples\": [\n          95.49119567871094,\n          137.06167602539062,\n          354.5032043457031\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"High\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 120.75746238664819,\n        \"min\": 90.72367122069444,\n        \"max\": 538.2820526128615,\n        \"num_unique_values\": 2516,\n        \"samples\": [\n          131.19207814048883,\n          178.6717438527183,\n          168.8787702306697\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Low\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 118.96230160200032,\n        \"min\": 78.7832293143827,\n        \"max\": 533.2700836043813,\n        \"num_unique_values\": 2516,\n        \"samples\": [\n          129.87189496452575,\n          177.41525067164383,\n          165.34906506467317\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Open\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 119.935200979066,\n        \"min\": 87.60613818634945,\n        \"max\": 535.4964739215987,\n        \"num_unique_values\": 2515,\n        \"samples\": [\n          130.92610613322395,\n          179.23767593885518,\n          166.95087453986113\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Volume\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22002320,\n        \"min\": 7079300,\n        \"max\": 198685800,\n        \"num_unique_values\": 2514,\n        \"samples\": [\n          24853500,\n          27371300,\n          52909300\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "markdown", "source": ["## **2. Adding Technical Indicators**\n", "We compute and add the following indicators:\n", "- **RSI (15-period)**: Evaluates recent price movements to identify momentum.\n", "- **EMA (20, 100, 150 periods)**: Used to identify short-, medium-, and long-term trends.\n"], "metadata": {"id": "0x_o-qqFybzJ"}}, {"cell_type": "code", "source": ["# Adding Indicators\n", "data['RSI'] = ta.rsi(data['Close'], length=15)\n", "data['EMAF'] = ta.ema(data['Close'], length=20)\n", "data['EMAM'] = ta.ema(data['Close'], length=100)\n", "data['EMAS'] = ta.ema(data['Close'], length=150)\n", "\n", "# Create Targets\n", "data['Target'] = data['Close'] - data['Open']\n", "data['Target'] = data['Target'].shift(-1)  # Predict next day's movement\n", "data['TargetClass'] = [1 if data['Target'][i] > 0 else 0 for i in range(len(data))]\n", "data['TargetNextClose'] = data['Close'].shift(-1)\n", "\n", "# Clean Missing Data\n", "data.dropna(inplace=True)\n", "data.reset_index(inplace=True)\n", "data.drop(['Volume', 'Date'], axis=1, inplace=True)\n", "\n", "# View Dataset\n", "data.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 258}, "id": "RJ4GQcxwydnN", "outputId": "47c9ddd5-5fec-460b-fac4-27c1754b1898"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-11-7ccbe9a9303e>:10: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  data['TargetClass'] = [1 if data['Target'][i] > 0 else 0 for i in range(len(data))]\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["Price      Close       High        Low       Open        RSI       EMAF  \\\n", "0      98.214050  98.279129  96.019946  97.172778  44.823030  99.520477   \n", "1      98.195450  98.548735  97.479573  97.693408  44.782108  99.394284   \n", "2      96.977547  98.186166  96.735828  97.646936  42.086539  99.164119   \n", "3      93.946701  96.029238  93.556220  94.560303  36.266040  98.667222   \n", "4      96.596336  96.614927  94.550981  95.648033  43.575276  98.469995   \n", "\n", "Price        EMAM        EMAS    Target  TargetClass  TargetNextClose  \n", "0      100.723249  100.265939  0.502042            1        98.195450  \n", "1      100.673193  100.238515 -0.669389            0        96.977547  \n", "2      100.600012  100.195324 -0.613602            0        93.946701  \n", "3      100.468263  100.112560  0.948303            1        96.596336  \n", "4      100.391592  100.065988 -0.855327            0        96.122208  "], "text/html": ["\n", "  <div id=\"df-56ac2ec5-881a-43c1-bc8e-9f169d1a8345\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>RSI</th>\n", "      <th>EMAF</th>\n", "      <th>EMAM</th>\n", "      <th>EMAS</th>\n", "      <th>Target</th>\n", "      <th>TargetClass</th>\n", "      <th>TargetNextClose</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>98.214050</td>\n", "      <td>98.279129</td>\n", "      <td>96.019946</td>\n", "      <td>97.172778</td>\n", "      <td>44.823030</td>\n", "      <td>99.520477</td>\n", "      <td>100.723249</td>\n", "      <td>100.265939</td>\n", "      <td>0.502042</td>\n", "      <td>1</td>\n", "      <td>98.195450</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>98.195450</td>\n", "      <td>98.548735</td>\n", "      <td>97.479573</td>\n", "      <td>97.693408</td>\n", "      <td>44.782108</td>\n", "      <td>99.394284</td>\n", "      <td>100.673193</td>\n", "      <td>100.238515</td>\n", "      <td>-0.669389</td>\n", "      <td>0</td>\n", "      <td>96.977547</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>96.977547</td>\n", "      <td>98.186166</td>\n", "      <td>96.735828</td>\n", "      <td>97.646936</td>\n", "      <td>42.086539</td>\n", "      <td>99.164119</td>\n", "      <td>100.600012</td>\n", "      <td>100.195324</td>\n", "      <td>-0.613602</td>\n", "      <td>0</td>\n", "      <td>93.946701</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>93.946701</td>\n", "      <td>96.029238</td>\n", "      <td>93.556220</td>\n", "      <td>94.560303</td>\n", "      <td>36.266040</td>\n", "      <td>98.667222</td>\n", "      <td>100.468263</td>\n", "      <td>100.112560</td>\n", "      <td>0.948303</td>\n", "      <td>1</td>\n", "      <td>96.596336</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>96.596336</td>\n", "      <td>96.614927</td>\n", "      <td>94.550981</td>\n", "      <td>95.648033</td>\n", "      <td>43.575276</td>\n", "      <td>98.469995</td>\n", "      <td>100.391592</td>\n", "      <td>100.065988</td>\n", "      <td>-0.855327</td>\n", "      <td>0</td>\n", "      <td>96.122208</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-56ac2ec5-881a-43c1-bc8e-9f169d1a8345')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-56ac2ec5-881a-43c1-bc8e-9f169d1a8345 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-56ac2ec5-881a-43c1-bc8e-9f169d1a8345');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-33ee4dff-6caf-49cc-8b4f-1106f70f7e08\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-33ee4dff-6caf-49cc-8b4f-1106f70f7e08')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-33ee4dff-6caf-49cc-8b4f-1106f70f7e08 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "data", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 2366,\n  \"fields\": [\n    {\n      \"column\": \"Close\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 118.06486423312538,\n        \"min\": 90.04122924804688,\n        \"max\": 537.3035888671875,\n        \"num_unique_values\": 2332,\n        \"samples\": [\n          145.45240783691406,\n          399.9971923828125,\n          470.0205383300781\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"High\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 118.88866105558047,\n        \"min\": 90.72367122069444,\n        \"max\": 538.2820526128615,\n        \"num_unique_values\": 2366,\n        \"samples\": [\n          198.95563460571455,\n          341.1743810858364,\n          162.0435604441598\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Low\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 117.14407621762466,\n        \"min\": 88.6577266951957,\n        \"max\": 533.2700836043813,\n        \"num_unique_values\": 2366,\n        \"samples\": [\n          197.39758850009744,\n          331.7052255967596,\n          160.03723732203505\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Open\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 118.09375508824095,\n        \"min\": 89.11576594977882,\n        \"max\": 535.4964739215987,\n        \"num_unique_values\": 2365,\n        \"samples\": [\n          187.64236424555315,\n          346.7607839500341,\n          367.5868110206015\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"RSI\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 11.262009148057734,\n        \"min\": 23.665770887251643,\n        \"max\": 84.54994446363222,\n        \"num_unique_values\": 2364,\n        \"samples\": [\n          31.67761766617374,\n          39.819638230295304,\n          36.57087885608277\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"EMAF\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 116.93407175156989,\n        \"min\": 94.0967613508531,\n        \"max\": 521.1442331912042,\n        \"num_unique_values\": 2366,\n        \"samples\": [\n          195.8616307588677,\n          345.3224671791226,\n          158.47179541933764\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"EMAM\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 112.51915718990446,\n        \"min\": 98.78459010212258,\n        \"max\": 502.0270213318045,\n        \"num_unique_values\": 2366,\n        \"samples\": [\n          187.86763557257166,\n          352.1840044497715,\n          154.90854066225137\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"EMAS\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 109.96814157914845,\n        \"min\": 99.20858959423241,\n        \"max\": 490.60675336773636,\n        \"num_unique_values\": 2366,\n        \"samples\": [\n          184.6785303155396,\n          352.9494801389264,\n          152.1191615471969\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Target\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2.980528921823212,\n        \"min\": -18.649980080213027,\n        \"max\": 21.21804675400392,\n        \"num_unique_values\": 2360,\n        \"samples\": [\n          2.4990615134158247,\n          -1.0162492020434968,\n          -3.2900821913070217\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"TargetClass\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"TargetNextClose\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 118.164616139944,\n        \"min\": 90.04122924804688,\n        \"max\": 537.3035888671875,\n        \"num_unique_values\": 2332,\n        \"samples\": [\n          146.10916137695312,\n          401.9307861328125\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "markdown", "source": ["## **3. Preprocessing Data**\n", "- Normalize features using `MinMaxScaler`.\n", "- Prepare input data for the LSTM model using a sliding window of 30 days."], "metadata": {"id": "p_UyvRt0yfyo"}}, {"cell_type": "code", "source": ["# Normalize Dataset\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "data_scaled = scaler.fit_transform(data)\n", "\n", "# Prepare Input for LSTM\n", "backcandles = 30\n", "X, y = [], []\n", "for i in range(backcandles, len(data_scaled)):\n", "    X.append(data_scaled[i-backcandles:i, :8])  # 8 features for predictors\n", "    y.append(data_scaled[i, -1])  # Target (Next Close Price)\n", "X, y = np.array(X), np.array(y)\n", "\n", "# Split Dataset\n", "split_limit = int(len(X) * 0.8)\n", "X_train, X_test = X[:split_limit], X[split_limit:]\n", "y_train, y_test = y[:split_limit], y[split_limit:]\n", "\n", "print(f\"Training Data Shape: {X_train.shape}, {y_train.shape}\")\n", "print(f\"Testing Data Shape: {X_test.shape}, {y_test.shape}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GXfPhhLpyilL", "outputId": "6b8a4438-afc6-4495-ac7c-ea1b2de28068"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Training Data Shape: (1868, 30, 8), (1868,)\n", "Testing Data Shape: (468, 30, 8), (468,)\n"]}]}, {"cell_type": "markdown", "source": ["## **4. Building and Training the LSTM Model**\n", "We use an LSTM network for time-series regression, as LSTMs are excellent at capturing sequential patterns in data.\n"], "metadata": {"id": "NOjkBCOsymvM"}}, {"cell_type": "code", "source": ["# Building the LSTM Model\n", "lstm_input = Input(shape=(backcandles, 8), name='lstm_input')\n", "lstm_layer = LSTM(150, name='lstm_layer')(lstm_input)\n", "dense_layer = Dense(1, name='dense_layer')(lstm_layer)\n", "output = Activation('linear', name='output')(dense_layer)\n", "\n", "model = Model(inputs=lstm_input, outputs=output)\n", "model.compile(optimizer=optimizers.<PERSON>(), loss='mse')\n", "\n", "# Training the Model\n", "model.fit(X_train, y_train, batch_size=15, epochs=30, shuffle=True, validation_split=0.1)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PGUpnqt0yL35", "outputId": "009bb467-5de6-4523-b0eb-09ec94353cba"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 1/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m10s\u001b[0m 64ms/step - loss: 0.0172 - val_loss: 5.4872e-04\n", "Epoch 2/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 26ms/step - loss: 2.1111e-04 - val_loss: 3.7498e-04\n", "Epoch 3/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 26ms/step - loss: 1.9432e-04 - val_loss: 3.5690e-04\n", "Epoch 4/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 34ms/step - loss: 2.2581e-04 - val_loss: 5.1998e-04\n", "Epoch 5/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 26ms/step - loss: 1.6314e-04 - val_loss: 3.3413e-04\n", "Epoch 6/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.4745e-04 - val_loss: 3.6531e-04\n", "Epoch 7/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.6226e-04 - val_loss: 3.0898e-04\n", "Epoch 8/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 33ms/step - loss: 1.5752e-04 - val_loss: 4.2219e-04\n", "Epoch 9/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 26ms/step - loss: 1.7564e-04 - val_loss: 3.5842e-04\n", "Epoch 10/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 38ms/step - loss: 1.4578e-04 - val_loss: 3.0415e-04\n", "Epoch 11/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 25ms/step - loss: 1.3490e-04 - val_loss: 3.4807e-04\n", "Epoch 12/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.2367e-04 - val_loss: 3.1047e-04\n", "Epoch 13/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.3330e-04 - val_loss: 3.5673e-04\n", "Epoch 14/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 42ms/step - loss: 1.9561e-04 - val_loss: 2.7721e-04\n", "Epoch 15/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.5026e-04 - val_loss: 2.7758e-04\n", "Epoch 16/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 27ms/step - loss: 1.2321e-04 - val_loss: 2.9330e-04\n", "Epoch 17/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m7s\u001b[0m 39ms/step - loss: 1.3146e-04 - val_loss: 2.7569e-04\n", "Epoch 18/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 30ms/step - loss: 1.6191e-04 - val_loss: 3.7501e-04\n", "Epoch 19/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 26ms/step - loss: 1.3516e-04 - val_loss: 3.1103e-04\n", "Epoch 20/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.5550e-04 - val_loss: 3.8167e-04\n", "Epoch 21/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 50ms/step - loss: 2.2513e-04 - val_loss: 2.6579e-04\n", "Epoch 22/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.2004e-04 - val_loss: 2.6163e-04\n", "Epoch 23/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.3176e-04 - val_loss: 2.7154e-04\n", "Epoch 24/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m7s\u001b[0m 43ms/step - loss: 1.1769e-04 - val_loss: 3.4256e-04\n", "Epoch 25/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.3363e-04 - val_loss: 4.1327e-04\n", "Epoch 26/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 26ms/step - loss: 1.5664e-04 - val_loss: 3.0992e-04\n", "Epoch 27/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 38ms/step - loss: 1.4661e-04 - val_loss: 2.5793e-04\n", "Epoch 28/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 25ms/step - loss: 1.1618e-04 - val_loss: 2.6491e-04\n", "Epoch 29/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 26ms/step - loss: 1.1918e-04 - val_loss: 2.7668e-04\n", "Epoch 30/30\n", "\u001b[1m113/113\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 35ms/step - loss: 1.5392e-04 - val_loss: 2.6359e-04\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["<keras.src.callbacks.history.History at 0x7dd597f7bb50>"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "markdown", "source": ["## **5. Model Evaluation and Visualization**\n", "We evaluate the model's predictions against the actual values."], "metadata": {"id": "prw4iV6UyqmU"}}, {"cell_type": "code", "source": ["# Predict on Test Set\n", "y_pred = model.predict(X_test)\n", "\n", "# Compare Predictions with Actual Values\n", "plt.figure(figsize=(16, 8))\n", "plt.plot(y_test, color='black', label='Actual')\n", "plt.plot(y_pred, color='green', label='Predicted')\n", "plt.title(\"Predicted vs. Actual Closing Prices\")\n", "plt.legend()\n", "plt.show()\n", "\n", "# Analyze Prediction Differences\n", "diff = y_test - y_pred.flatten()\n", "plt.figure(figsize=(16, 8))\n", "plt.plot(diff, color='red', label='Difference')\n", "plt.title(\"Prediction Differences\")\n", "plt.legend()\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "5Ju9YLeVyrFd", "outputId": "fcbfd171-1187-4fc8-abef-6377f5f00b92"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[1m15/15\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 53ms/step\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1600x800 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1600x800 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["## **6. Observations and Improvements**\n", "• **RSI and EMA Effectiveness**: Indicators help in capturing momentum and trend, hence, helping the model learn better.\n", "• **Model Predictions**: LSTM can keep pace with the price trend; however, more tuning seems necessary for it to be reasonably correct, as perhaps more number of epochs, neurons, or additional indicators can further improve performance.\n", "• **Possible Improvements:**\n", "  • More Indicators\n", "  • Tuning of Hyperparameter - LSTM Units and Batch Size\n", "  • Longer window of `backcandles`.\n", "\n", "##**Conclusion**\n", "This project is an example of how LSTM models can predict stock price movements based on historical data and technical indicators. Although promising, it requires further refinements to make it production-grade.\n"], "metadata": {"id": "qMluhK2dywxq"}}, {"cell_type": "markdown", "source": ["---\n", "## **Credits**\n", "- Dataset provided by **Yahoo Finance** via the `yfinance` Python library.\n", "- Technical indicators calculated using the `pandas-ta` library.\n", "- LSTM implementation powered by **TensorFlow/Keras**.\n", "- Special thanks to me *<PERSON>* who made this project possible.\n", "\n", "---\n"], "metadata": {"id": "tphTHROYzHmB"}}]}