"""
고급 거래량 및 매물대 분석 시스템
Advanced Volume and Support/Resistance Analysis System
"""

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class AdvancedTradingAnalyzer:
    def __init__(self):
        self.data = None
        self.symbol = None
        self.market_type = None
        
    def detect_market_type(self, symbol):
        """시장 유형 자동 감지"""
        if symbol.endswith('.KS') or symbol.endswith('.KQ'):
            return 'KOREAN'
        elif '-USD' in symbol or symbol in ['BTC-USD', 'ETH-USD', 'ADA-USD']:
            return 'CRYPTO'
        else:
            return 'US'
    
    def get_multi_timeframe_data(self, symbol, period="3mo"):
        """다중 시간대 데이터 수집"""
        self.symbol = symbol
        self.market_type = self.detect_market_type(symbol)
        
        try:
            ticker = yf.Ticker(symbol)
            
            # 다양한 시간대 데이터 수집
            timeframes = {
                '1h': ticker.history(period=period, interval="1h"),
                '2h': ticker.history(period=period, interval="2h"), 
                '4h': ticker.history(period=period, interval="4h"),
                '1d': ticker.history(period=period, interval="1d")
            }
            
            # 데이터 검증
            for tf, data in timeframes.items():
                if data.empty:
                    print(f"⚠️ {tf} 데이터를 가져올 수 없습니다.")
                else:
                    print(f"✅ {tf} 데이터: {len(data)}개 캔들")
            
            self.data = timeframes
            return timeframes
            
        except Exception as e:
            print(f"❌ 데이터 수집 실패: {e}")
            return None
    
    def analyze_volume_profile(self, timeframe='1d', bins=50):
        """거래량 프로파일 분석 (매물대 분석)"""
        if not self.data or timeframe not in self.data:
            print("데이터가 없습니다.")
            return None
            
        df = self.data[timeframe].copy()
        
        # 가격 범위별 거래량 집계
        price_min = df['Low'].min()
        price_max = df['High'].max()
        price_bins = np.linspace(price_min, price_max, bins)
        
        volume_profile = []
        
        for i in range(len(price_bins)-1):
            bin_low = price_bins[i]
            bin_high = price_bins[i+1]
            bin_center = (bin_low + bin_high) / 2
            
            # 해당 가격대에서 거래된 총 거래량 계산
            mask = (df['Low'] <= bin_high) & (df['High'] >= bin_low)
            total_volume = df[mask]['Volume'].sum()
            
            volume_profile.append({
                'price': bin_center,
                'volume': total_volume,
                'price_range': f"{bin_low:.2f}-{bin_high:.2f}"
            })
        
        volume_df = pd.DataFrame(volume_profile)
        volume_df = volume_df.sort_values('volume', ascending=False)
        
        # 주요 매물대 식별 (상위 20%)
        top_20_pct = int(len(volume_df) * 0.2)
        major_levels = volume_df.head(top_20_pct)
        
        return {
            'volume_profile': volume_df,
            'major_support_resistance': major_levels,
            'poc': volume_df.iloc[0]['price'],  # Point of Control (최대 거래량 가격)
            'current_price': df['Close'].iloc[-1]
        }
    
    def analyze_candle_patterns(self, timeframe='1h'):
        """캔들 패턴 분석"""
        if not self.data or timeframe not in self.data:
            return None
            
        df = self.data[timeframe].copy()
        
        # 캔들 특성 계산
        df['body_size'] = abs(df['Close'] - df['Open'])
        df['upper_shadow'] = df['High'] - df[['Open', 'Close']].max(axis=1)
        df['lower_shadow'] = df[['Open', 'Close']].min(axis=1) - df['Low']
        df['total_range'] = df['High'] - df['Low']
        df['body_ratio'] = df['body_size'] / df['total_range']
        
        # 캔들 패턴 식별
        patterns = []
        
        for i in range(2, len(df)):
            current = df.iloc[i]
            prev = df.iloc[i-1]
            prev2 = df.iloc[i-2]
            
            pattern = self.identify_candle_pattern(current, prev, prev2)
            if pattern:
                patterns.append({
                    'timestamp': current.name,
                    'pattern': pattern,
                    'price': current['Close'],
                    'volume': current['Volume'],
                    'significance': self.calculate_pattern_significance(current, prev)
                })
        
        return pd.DataFrame(patterns)
    
    def identify_candle_pattern(self, current, prev, prev2):
        """개별 캔들 패턴 식별"""
        # 도지
        if current['body_ratio'] < 0.1:
            return 'DOJI'
        
        # 해머/행잉맨
        if (current['lower_shadow'] > current['body_size'] * 2 and 
            current['upper_shadow'] < current['body_size'] * 0.5):
            if current['Close'] > current['Open']:
                return 'HAMMER'
            else:
                return 'HANGING_MAN'
        
        # 유성/역망치
        if (current['upper_shadow'] > current['body_size'] * 2 and 
            current['lower_shadow'] < current['body_size'] * 0.5):
            return 'SHOOTING_STAR'
        
        # 엔걸핑 패턴
        if (current['body_size'] > prev['body_size'] * 1.5 and
            current['Open'] < prev['Close'] and current['Close'] > prev['Open']):
            return 'BULLISH_ENGULFING'
        
        if (current['body_size'] > prev['body_size'] * 1.5 and
            current['Open'] > prev['Close'] and current['Close'] < prev['Open']):
            return 'BEARISH_ENGULFING'
        
        return None
    
    def calculate_pattern_significance(self, current, prev):
        """패턴 중요도 계산"""
        volume_ratio = current['Volume'] / prev['Volume'] if prev['Volume'] > 0 else 1
        price_change = abs(current['Close'] - prev['Close']) / prev['Close']
        
        return volume_ratio * price_change * 100
    
    def analyze_volume_flow(self, timeframe='1h'):
        """자금 흐름 분석"""
        if not self.data or timeframe not in self.data:
            return None
            
        df = self.data[timeframe].copy()
        
        # OBV (On Balance Volume) 계산
        df['price_change'] = df['Close'].diff()
        df['obv'] = 0
        
        for i in range(1, len(df)):
            if df['price_change'].iloc[i] > 0:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1] + df['Volume'].iloc[i]
            elif df['price_change'].iloc[i] < 0:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1] - df['Volume'].iloc[i]
            else:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1]
        
        # VWAP (Volume Weighted Average Price) 계산
        df['typical_price'] = (df['High'] + df['Low'] + df['Close']) / 3
        df['volume_price'] = df['typical_price'] * df['Volume']
        df['cumulative_volume'] = df['Volume'].cumsum()
        df['cumulative_volume_price'] = df['volume_price'].cumsum()
        df['vwap'] = df['cumulative_volume_price'] / df['cumulative_volume']
        
        # 자금 흐름 지수 (MFI) 계산
        df['raw_money_flow'] = df['typical_price'] * df['Volume']
        
        positive_flow = []
        negative_flow = []
        
        for i in range(1, len(df)):
            if df['typical_price'].iloc[i] > df['typical_price'].iloc[i-1]:
                positive_flow.append(df['raw_money_flow'].iloc[i])
                negative_flow.append(0)
            elif df['typical_price'].iloc[i] < df['typical_price'].iloc[i-1]:
                positive_flow.append(0)
                negative_flow.append(df['raw_money_flow'].iloc[i])
            else:
                positive_flow.append(0)
                negative_flow.append(0)
        
        df = df.iloc[1:].copy()  # 첫 번째 행 제거
        df['positive_flow'] = positive_flow
        df['negative_flow'] = negative_flow
        
        # 14일 MFI 계산
        period = min(14, len(df))
        df['positive_flow_sum'] = df['positive_flow'].rolling(period).sum()
        df['negative_flow_sum'] = df['negative_flow'].rolling(period).sum()
        df['money_flow_ratio'] = df['positive_flow_sum'] / df['negative_flow_sum']
        df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))
        
        return df[['Close', 'Volume', 'obv', 'vwap', 'mfi']].tail(50)
    
    def find_support_resistance_levels(self, timeframe='1d', window=20):
        """지지/저항선 자동 탐지"""
        if not self.data or timeframe not in self.data:
            return None
            
        df = self.data[timeframe].copy()
        
        # 피벗 포인트 찾기
        highs = df['High'].rolling(window=window, center=True).max()
        lows = df['Low'].rolling(window=window, center=True).min()
        
        resistance_levels = []
        support_levels = []
        
        for i in range(window, len(df) - window):
            # 저항선 (고점)
            if df['High'].iloc[i] == highs.iloc[i]:
                resistance_levels.append({
                    'price': df['High'].iloc[i],
                    'timestamp': df.index[i],
                    'volume': df['Volume'].iloc[i],
                    'strength': self.calculate_level_strength(df, i, 'High', window)
                })
            
            # 지지선 (저점)
            if df['Low'].iloc[i] == lows.iloc[i]:
                support_levels.append({
                    'price': df['Low'].iloc[i],
                    'timestamp': df.index[i],
                    'volume': df['Volume'].iloc[i],
                    'strength': self.calculate_level_strength(df, i, 'Low', window)
                })
        
        # 강도순으로 정렬
        resistance_df = pd.DataFrame(resistance_levels).sort_values('strength', ascending=False)
        support_df = pd.DataFrame(support_levels).sort_values('strength', ascending=False)
        
        return {
            'resistance_levels': resistance_df.head(10),
            'support_levels': support_df.head(10),
            'current_price': df['Close'].iloc[-1]
        }
    
    def calculate_level_strength(self, df, index, price_type, window):
        """지지/저항선 강도 계산"""
        price = df[price_type].iloc[index]
        volume = df['Volume'].iloc[index]
        
        # 해당 가격 근처에서의 터치 횟수
        price_tolerance = price * 0.02  # 2% 허용 오차
        nearby_touches = 0
        
        for i in range(max(0, index - window*2), min(len(df), index + window*2)):
            if abs(df[price_type].iloc[i] - price) <= price_tolerance:
                nearby_touches += 1
        
        # 거래량과 터치 횟수를 고려한 강도 계산
        strength = (volume / df['Volume'].mean()) * nearby_touches
        
        return strength
    
    def generate_trading_signals(self, timeframes=['1h', '4h', '1d']):
        """다중 시간대 매매 신호 생성"""
        signals = {}
        
        for tf in timeframes:
            if tf not in self.data:
                continue
                
            df = self.data[tf].copy()
            
            # 기술적 지표 계산
            df = self.add_technical_indicators(df)
            
            # 신호 생성
            current = df.iloc[-1]
            signal_strength = 0
            reasons = []
            
            # RSI 신호
            if current['RSI'] < 30:
                signal_strength += 1
                reasons.append(f"RSI 과매도 ({current['RSI']:.1f})")
            elif current['RSI'] > 70:
                signal_strength -= 1
                reasons.append(f"RSI 과매수 ({current['RSI']:.1f})")
            
            # 볼린저 밴드 신호
            if current['Close'] < current['BB_lower']:
                signal_strength += 1
                reasons.append("볼린저 밴드 하단 터치")
            elif current['Close'] > current['BB_upper']:
                signal_strength -= 1
                reasons.append("볼린저 밴드 상단 터치")
            
            # 거래량 신호
            volume_avg = df['Volume'].rolling(20).mean().iloc[-1]
            if current['Volume'] > volume_avg * 1.5:
                signal_strength += 0.5
                reasons.append("높은 거래량")
            
            signals[tf] = {
                'signal_strength': signal_strength,
                'reasons': reasons,
                'current_price': current['Close'],
                'volume': current['Volume']
            }
        
        return signals
    
    def add_technical_indicators(self, df):
        """기술적 지표 추가"""
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # 볼린저 밴드
        df['BB_middle'] = df['Close'].rolling(20).mean()
        bb_std = df['Close'].rolling(20).std()
        df['BB_upper'] = df['BB_middle'] + (bb_std * 2)
        df['BB_lower'] = df['BB_middle'] - (bb_std * 2)
        
        return df

    def visualize_analysis(self, timeframe='1d'):
        """종합 분석 시각화"""
        if not self.data or timeframe not in self.data:
            print("데이터가 없습니다.")
            return

        fig, axes = plt.subplots(2, 2, figsize=(20, 15))
        fig.suptitle(f'{self.symbol} - {timeframe} 종합 분석', fontsize=16, fontweight='bold')

        # 1. 가격 차트 + 거래량
        ax1 = axes[0, 0]
        df = self.data[timeframe].copy()
        df = self.add_technical_indicators(df)

        ax1.plot(df.index, df['Close'], label='종가', linewidth=2)
        ax1.plot(df.index, df['BB_upper'], label='볼린저 상단', alpha=0.7)
        ax1.plot(df.index, df['BB_lower'], label='볼린저 하단', alpha=0.7)
        ax1.fill_between(df.index, df['BB_upper'], df['BB_lower'], alpha=0.1)
        ax1.set_title('가격 차트 + 볼린저 밴드')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 거래량 프로파일
        ax2 = axes[0, 1]
        volume_analysis = self.analyze_volume_profile(timeframe)
        if volume_analysis:
            vp = volume_analysis['volume_profile']
            ax2.barh(vp['price'], vp['volume'], alpha=0.7)
            ax2.axhline(y=volume_analysis['current_price'], color='red',
                       linestyle='--', label=f"현재가: {volume_analysis['current_price']:.2f}")
            ax2.axhline(y=volume_analysis['poc'], color='green',
                       linestyle='--', label=f"POC: {volume_analysis['poc']:.2f}")
            ax2.set_title('거래량 프로파일 (매물대)')
            ax2.legend()

        # 3. RSI
        ax3 = axes[1, 0]
        ax3.plot(df.index, df['RSI'], label='RSI', color='purple')
        ax3.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='과매수')
        ax3.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='과매도')
        ax3.fill_between(df.index, 30, 70, alpha=0.1)
        ax3.set_title('RSI (상대강도지수)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 거래량
        ax4 = axes[1, 1]
        colors = ['red' if close < open else 'green' for close, open in zip(df['Close'], df['Open'])]
        ax4.bar(df.index, df['Volume'], color=colors, alpha=0.7)
        volume_ma = df['Volume'].rolling(20).mean()
        ax4.plot(df.index, volume_ma, color='blue', label='거래량 이평선(20)')
        ax4.set_title('거래량')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def print_comprehensive_analysis(self):
        """종합 분석 결과 출력"""
        print(f"\n{'='*60}")
        print(f"📊 {self.symbol} 종합 분석 보고서")
        print(f"시장: {self.market_type} | 분석 시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")

        # 1. 거래량 프로파일 분석
        print("\n🎯 매물대 분석 (거래량 프로파일)")
        volume_analysis = self.analyze_volume_profile('1d')
        if volume_analysis:
            current_price = volume_analysis['current_price']
            poc = volume_analysis['poc']
            major_levels = volume_analysis['major_support_resistance']

            print(f"현재가: ${current_price:.2f}")
            print(f"POC (최대 거래량 가격): ${poc:.2f}")
            print(f"POC 대비 현재가: {((current_price - poc) / poc * 100):+.2f}%")

            print("\n주요 매물대 (상위 5개):")
            for i, level in major_levels.head(5).iterrows():
                distance = ((current_price - level['price']) / level['price'] * 100)
                print(f"  ${level['price']:.2f} (현재가 대비 {distance:+.2f}%) - 거래량: {level['volume']:,.0f}")

        # 2. 지지/저항선 분석
        print("\n📈 지지/저항선 분석")
        sr_analysis = self.find_support_resistance_levels('1d')
        if sr_analysis:
            current_price = sr_analysis['current_price']

            print("주요 저항선:")
            resistance = sr_analysis['resistance_levels'].head(3)
            for i, level in resistance.iterrows():
                distance = ((level['price'] - current_price) / current_price * 100)
                print(f"  ${level['price']:.2f} (+{distance:.2f}%) - 강도: {level['strength']:.1f}")

            print("주요 지지선:")
            support = sr_analysis['support_levels'].head(3)
            for i, level in support.iterrows():
                distance = ((current_price - level['price']) / level['price'] * 100)
                print(f"  ${level['price']:.2f} (-{distance:.2f}%) - 강도: {level['strength']:.1f}")

        # 3. 자금 흐름 분석
        print("\n💰 자금 흐름 분석")
        flow_analysis = self.analyze_volume_flow('1h')
        if flow_analysis is not None and not flow_analysis.empty:
            latest = flow_analysis.iloc[-1]
            print(f"VWAP: ${latest['vwap']:.2f}")
            print(f"현재가 vs VWAP: {((latest['Close'] - latest['vwap']) / latest['vwap'] * 100):+.2f}%")
            print(f"MFI (자금흐름지수): {latest['mfi']:.1f}")

            if latest['mfi'] > 80:
                print("  → 과매수 상태 (매도 압력 증가 가능)")
            elif latest['mfi'] < 20:
                print("  → 과매도 상태 (매수 기회 가능)")
            else:
                print("  → 중립 상태")

        # 4. 다중 시간대 신호
        print("\n⚡ 다중 시간대 매매 신호")
        signals = self.generate_trading_signals(['1h', '4h', '1d'])

        total_signal = 0
        for tf, signal_data in signals.items():
            strength = signal_data['signal_strength']
            total_signal += strength

            if strength > 1:
                signal_text = "🟢 강한 매수"
            elif strength > 0:
                signal_text = "🔵 약한 매수"
            elif strength < -1:
                signal_text = "🔴 강한 매도"
            elif strength < 0:
                signal_text = "🟡 약한 매도"
            else:
                signal_text = "⚪ 중립"

            print(f"{tf}: {signal_text} (강도: {strength:.1f})")
            if signal_data['reasons']:
                for reason in signal_data['reasons']:
                    print(f"    - {reason}")

        # 5. 종합 판단
        print(f"\n🎯 종합 매매 신호")
        if total_signal > 2:
            print("🟢 강한 매수 신호")
        elif total_signal > 0:
            print("🔵 약한 매수 신호")
        elif total_signal < -2:
            print("🔴 강한 매도 신호")
        elif total_signal < 0:
            print("🟡 약한 매도 신호")
        else:
            print("⚪ 중립 - 관망 권장")

        print(f"신호 강도: {total_signal:.1f}")

        # 6. 캔들 패턴 분석
        print("\n🕯️ 최근 캔들 패턴")
        patterns = self.analyze_candle_patterns('1h')
        if patterns is not None and not patterns.empty:
            recent_patterns = patterns.tail(5)
            for i, pattern in recent_patterns.iterrows():
                print(f"  {pattern['timestamp'].strftime('%m-%d %H:%M')}: {pattern['pattern']} "
                      f"(중요도: {pattern['significance']:.1f})")
        else:
            print("  특별한 패턴이 감지되지 않았습니다.")


def main():
    """메인 실행 함수"""
    analyzer = AdvancedTradingAnalyzer()

    print("🚀 고급 거래량 및 매물대 분석 시스템")
    print("=" * 50)
    print("지원 시장:")
    print("  - 미국 주식: AAPL, TSLA, NVDA, QQQ, SPY 등")
    print("  - 한국 주식: 005930.KS (삼성전자), 000660.KS (SK하이닉스) 등")
    print("  - 암호화폐: BTC-USD, ETH-USD, ADA-USD 등")
    print("=" * 50)

    while True:
        symbol = input("\n분석할 종목 코드를 입력하세요 (종료: 'quit'): ").strip().upper()

        if symbol.lower() == 'quit':
            print("프로그램을 종료합니다.")
            break

        if not symbol:
            print("올바른 종목 코드를 입력해주세요.")
            continue

        print(f"\n📊 {symbol} 데이터 수집 중...")

        # 데이터 수집
        data = analyzer.get_multi_timeframe_data(symbol)

        if not data:
            print("❌ 데이터 수집에 실패했습니다. 다른 종목을 시도해보세요.")
            continue

        # 종합 분석 실행
        analyzer.print_comprehensive_analysis()

        # 시각화 옵션
        show_chart = input("\n차트를 표시하시겠습니까? (y/n): ").strip().lower()
        if show_chart == 'y':
            analyzer.visualize_analysis('1d')

        print("\n" + "="*60)


if __name__ == "__main__":
    main()
