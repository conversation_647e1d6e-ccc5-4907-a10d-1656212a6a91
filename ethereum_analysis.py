"""
이더리움(ETHUSDT) 실시간 거래량 및 매물대 분석
Ethereum Real-time Volume and Order Book Analysis
"""

import urllib.request
import json
import pandas as pd
from datetime import datetime, timedelta
import time

def get_ethereum_ticker():
    """이더리움 현재가 정보"""
    try:
        url = "https://api.bybit.com/v5/market/tickers?category=spot&symbol=ETHUSDT"
        
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode())
        
        if data['retCode'] == 0:
            return data['result']['list'][0]
        return None
    except Exception as e:
        print(f"❌ 이더리움 현재가 조회 실패: {e}")
        return None

def get_ethereum_klines(interval='1h', limit=100):
    """이더리움 캔들 데이터"""
    try:
        url = f"https://api.bybit.com/v5/market/kline?category=spot&symbol=ETHUSDT&interval={interval}&limit={limit}"
        
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode())
        
        if data['retCode'] == 0:
            klines = data['result']['list']
            
            df_data = []
            for kline in klines:
                df_data.append({
                    'timestamp': pd.to_datetime(int(kline[0]), unit='ms'),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5]),
                    'turnover': float(kline[6])
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            return df
        return None
    except Exception as e:
        print(f"❌ 이더리움 캔들 데이터 조회 실패: {e}")
        return None

def get_ethereum_orderbook():
    """이더리움 호가창"""
    try:
        url = "https://api.bybit.com/v5/market/orderbook?category=spot&symbol=ETHUSDT&limit=25"
        
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode())
        
        if data['retCode'] == 0:
            result = data['result']
            return {
                'bids': [[float(bid[0]), float(bid[1])] for bid in result['b']],
                'asks': [[float(ask[0]), float(ask[1])] for ask in result['a']],
                'timestamp': pd.to_datetime(int(result['ts']), unit='ms')
            }
        return None
    except Exception as e:
        print(f"❌ 이더리움 호가창 조회 실패: {e}")
        return None

def get_ethereum_trades():
    """이더리움 최근 거래"""
    try:
        url = "https://api.bybit.com/v5/market/recent-trade?category=spot&symbol=ETHUSDT&limit=50"
        
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode())
        
        if data['retCode'] == 0:
            trades = data['result']['list']
            
            df_data = []
            for trade in trades:
                df_data.append({
                    'timestamp': pd.to_datetime(int(trade['time']), unit='ms'),
                    'price': float(trade['price']),
                    'size': float(trade['size']),
                    'side': trade['side']
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            return df
        return None
    except Exception as e:
        print(f"❌ 이더리움 거래내역 조회 실패: {e}")
        return None

def analyze_volume_profile(df, bins=20):
    """거래량 프로파일 분석"""
    if df is None or df.empty:
        return None
    
    price_min = df['low'].min()
    price_max = df['high'].max()
    price_bins = pd.cut(df['close'], bins=bins, include_lowest=True)
    
    volume_profile = df.groupby(price_bins)['volume'].sum().sort_values(ascending=False)
    
    # POC (Point of Control) - 최대 거래량 가격대
    poc_bin = volume_profile.index[0]
    poc_price = (poc_bin.left + poc_bin.right) / 2
    
    current_price = df['close'].iloc[-1]
    
    return {
        'volume_profile': volume_profile,
        'poc_price': poc_price,
        'current_price': current_price,
        'poc_distance': ((current_price - poc_price) / poc_price * 100)
    }

def analyze_support_resistance(df, window=20):
    """지지/저항선 분석"""
    if df is None or df.empty or len(df) < window:
        return None
    
    # 피벗 포인트 계산
    highs = df['high'].rolling(window=window, center=True).max()
    lows = df['low'].rolling(window=window, center=True).min()
    
    resistance_levels = []
    support_levels = []
    
    for i in range(window, len(df) - window):
        if df['high'].iloc[i] == highs.iloc[i]:
            resistance_levels.append({
                'price': df['high'].iloc[i],
                'volume': df['volume'].iloc[i],
                'date': df.index[i]
            })
        
        if df['low'].iloc[i] == lows.iloc[i]:
            support_levels.append({
                'price': df['low'].iloc[i],
                'volume': df['volume'].iloc[i],
                'date': df.index[i]
            })
    
    # 최근 3개씩 반환
    resistance_df = pd.DataFrame(resistance_levels).tail(3) if resistance_levels else pd.DataFrame()
    support_df = pd.DataFrame(support_levels).tail(3) if support_levels else pd.DataFrame()
    
    return {
        'resistance': resistance_df,
        'support': support_df,
        'current_price': df['close'].iloc[-1]
    }

def calculate_technical_indicators(df):
    """기술적 지표 계산"""
    if df is None or df.empty:
        return None
    
    # RSI 계산
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    # 볼린저 밴드
    bb_period = 20
    bb_middle = df['close'].rolling(bb_period).mean()
    bb_std = df['close'].rolling(bb_period).std()
    bb_upper = bb_middle + (bb_std * 2)
    bb_lower = bb_middle - (bb_std * 2)
    
    # 이동평균
    sma_20 = df['close'].rolling(20).mean()
    sma_50 = df['close'].rolling(50).mean()
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    
    # MACD
    macd = ema_12 - ema_26
    macd_signal = macd.ewm(span=9).mean()
    macd_histogram = macd - macd_signal
    
    # VWAP
    typical_price = (df['high'] + df['low'] + df['close']) / 3
    vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
    
    current = df.iloc[-1]
    
    return {
        'rsi': rsi.iloc[-1],
        'bb_upper': bb_upper.iloc[-1],
        'bb_lower': bb_lower.iloc[-1],
        'bb_middle': bb_middle.iloc[-1],
        'sma_20': sma_20.iloc[-1],
        'sma_50': sma_50.iloc[-1],
        'macd': macd.iloc[-1],
        'macd_signal': macd_signal.iloc[-1],
        'macd_histogram': macd_histogram.iloc[-1],
        'vwap': vwap.iloc[-1],
        'current_price': current['close'],
        'volume': current['volume']
    }

def analyze_candle_patterns(df):
    """캔들 패턴 분석"""
    if df is None or df.empty or len(df) < 3:
        return []
    
    patterns = []
    recent_data = df.tail(10)  # 최근 10개 캔들
    
    for i in range(1, len(recent_data)):
        current = recent_data.iloc[i]
        prev = recent_data.iloc[i-1]
        
        # 캔들 특성 계산
        body_size = abs(current['close'] - current['open'])
        total_range = current['high'] - current['low']
        upper_shadow = current['high'] - max(current['open'], current['close'])
        lower_shadow = min(current['open'], current['close']) - current['low']
        
        if total_range == 0:
            continue
            
        body_ratio = body_size / total_range
        
        # 패턴 식별
        pattern = None
        significance = 0
        
        # 도지
        if body_ratio < 0.1:
            pattern = "DOJI"
            significance = 3
        
        # 해머
        elif (lower_shadow > body_size * 2 and upper_shadow < body_size * 0.5):
            if current['close'] > current['open']:
                pattern = "HAMMER"
                significance = 7
            else:
                pattern = "HANGING_MAN"
                significance = 6
        
        # 유성
        elif (upper_shadow > body_size * 2 and lower_shadow < body_size * 0.5):
            pattern = "SHOOTING_STAR"
            significance = 6
        
        # 엔걸핑
        elif (body_size > abs(prev['close'] - prev['open']) * 1.5):
            if (current['open'] < prev['close'] and current['close'] > prev['open']):
                pattern = "BULLISH_ENGULFING"
                significance = 8
            elif (current['open'] > prev['close'] and current['close'] < prev['open']):
                pattern = "BEARISH_ENGULFING"
                significance = 8
        
        if pattern:
            patterns.append({
                'timestamp': current.name,
                'pattern': pattern,
                'price': current['close'],
                'volume': current['volume'],
                'significance': significance
            })
    
    return patterns

def generate_trading_signals(ticker, indicators, orderbook, trades, volume_profile):
    """매매 신호 생성"""
    signals = []
    signal_strength = 0
    
    if not all([ticker, indicators, orderbook, trades]):
        return signals, 0
    
    current_price = float(ticker['lastPrice'])
    
    # 1. RSI 신호
    rsi = indicators['rsi']
    if rsi < 30:
        signals.append("RSI 과매도 (매수 신호)")
        signal_strength += 1
    elif rsi > 70:
        signals.append("RSI 과매수 (매도 신호)")
        signal_strength -= 1
    
    # 2. 볼린저 밴드 신호
    if current_price < indicators['bb_lower']:
        signals.append("볼린저 밴드 하단 터치 (매수 신호)")
        signal_strength += 1
    elif current_price > indicators['bb_upper']:
        signals.append("볼린저 밴드 상단 돌파 (매도 신호)")
        signal_strength -= 1
    
    # 3. MACD 신호
    if indicators['macd'] > indicators['macd_signal'] and indicators['macd_histogram'] > 0:
        signals.append("MACD 골든크로스 (매수 신호)")
        signal_strength += 0.5
    elif indicators['macd'] < indicators['macd_signal'] and indicators['macd_histogram'] < 0:
        signals.append("MACD 데드크로스 (매도 신호)")
        signal_strength -= 0.5
    
    # 4. 이동평균 신호
    if current_price > indicators['sma_20'] > indicators['sma_50']:
        signals.append("이동평균 정배열 (상승 추세)")
        signal_strength += 0.5
    elif current_price < indicators['sma_20'] < indicators['sma_50']:
        signals.append("이동평균 역배열 (하락 추세)")
        signal_strength -= 0.5
    
    # 5. VWAP 신호
    vwap_diff = (current_price - indicators['vwap']) / indicators['vwap'] * 100
    if vwap_diff > 2:
        signals.append(f"VWAP 상향 이탈 +{vwap_diff:.2f}% (과매수 주의)")
        signal_strength -= 0.3
    elif vwap_diff < -2:
        signals.append(f"VWAP 하향 이탈 {vwap_diff:.2f}% (매수 기회)")
        signal_strength += 0.3
    
    # 6. 호가창 신호
    bids = orderbook['bids'][:10]
    asks = orderbook['asks'][:10]
    
    total_bid_volume = sum([bid[1] for bid in bids])
    total_ask_volume = sum([ask[1] for ask in asks])
    
    if total_bid_volume > total_ask_volume * 1.5:
        signals.append("호가창 매수 벽 우세 (매수 압력)")
        signal_strength += 0.5
    elif total_ask_volume > total_bid_volume * 1.5:
        signals.append("호가창 매도 벽 우세 (매도 압력)")
        signal_strength -= 0.5
    
    # 7. 거래량 신호
    volume_24h = float(ticker['volume24h'])
    if volume_24h > 100000:  # 10만 ETH 이상
        signals.append("높은 거래량 (관심 증가)")
        signal_strength += 0.3
    
    # 8. 최근 거래 압력
    if not trades.empty:
        buy_trades = trades[trades['side'] == 'Buy']
        sell_trades = trades[trades['side'] == 'Sell']
        
        buy_volume = buy_trades['size'].sum()
        sell_volume = sell_trades['size'].sum()
        
        if buy_volume > sell_volume * 1.3:
            signals.append("최근 거래 매수 우세")
            signal_strength += 0.3
        elif sell_volume > buy_volume * 1.3:
            signals.append("최근 거래 매도 우세")
            signal_strength -= 0.3
    
    return signals, signal_strength

def main():
    """이더리움 종합 분석"""
    print("🔷 이더리움(ETHUSDT) 실시간 거래량 및 매물대 분석")
    print("=" * 60)
    
    # 1. 기본 정보 수집
    print("📊 데이터 수집 중...")
    ticker = get_ethereum_ticker()
    
    if not ticker:
        print("❌ 이더리움 데이터 수집 실패")
        return
    
    # 2. 다중 시간대 데이터
    timeframes = {
        '1시간': get_ethereum_klines('1h', 100),
        '4시간': get_ethereum_klines('4h', 100),
        '일봉': get_ethereum_klines('D', 100)
    }
    
    # 3. 호가창 및 거래 데이터
    orderbook = get_ethereum_orderbook()
    trades = get_ethereum_trades()
    
    # 기본 정보 출력
    current_price = float(ticker['lastPrice'])
    price_change_24h = float(ticker['price24hPcnt']) * 100
    volume_24h = float(ticker['volume24h'])
    turnover_24h = float(ticker['turnover24h'])
    
    print(f"\n💰 이더리움 기본 정보:")
    print(f"현재가: ${current_price:,.2f}")
    print(f"24시간 변동: {price_change_24h:+.2f}%")
    print(f"24시간 거래량: {volume_24h:,.2f} ETH")
    print(f"24시간 거래대금: ${turnover_24h:,.2f}")
    print(f"24시간 최고가: ${float(ticker['highPrice24h']):,.2f}")
    print(f"24시간 최저가: ${float(ticker['lowPrice24h']):,.2f}")
    
    # 기술적 지표 분석
    daily_data = timeframes['일봉']
    if daily_data is not None and not daily_data.empty:
        print(f"\n📈 기술적 지표 분석:")
        indicators = calculate_technical_indicators(daily_data)
        
        if indicators:
            print(f"RSI: {indicators['rsi']:.1f}", end="")
            if indicators['rsi'] > 70:
                print(" (과매수)")
            elif indicators['rsi'] < 30:
                print(" (과매도)")
            else:
                print(" (중립)")
            
            print(f"볼린저 밴드: ${indicators['bb_lower']:.2f} ~ ${indicators['bb_upper']:.2f}")
            bb_position = (current_price - indicators['bb_lower']) / (indicators['bb_upper'] - indicators['bb_lower'])
            print(f"  → 밴드 내 위치: {bb_position:.1%}")
            
            print(f"VWAP: ${indicators['vwap']:.2f}")
            vwap_diff = (current_price - indicators['vwap']) / indicators['vwap'] * 100
            print(f"  → 현재가 vs VWAP: {vwap_diff:+.2f}%")
            
            print(f"20일 이평선: ${indicators['sma_20']:.2f} ({(current_price - indicators['sma_20'])/indicators['sma_20']*100:+.2f}%)")
            print(f"50일 이평선: ${indicators['sma_50']:.2f} ({(current_price - indicators['sma_50'])/indicators['sma_50']*100:+.2f}%)")
    
    # 거래량 프로파일 분석
    if daily_data is not None and not daily_data.empty:
        print(f"\n🎯 거래량 프로파일 (매물대) 분석:")
        volume_profile = analyze_volume_profile(daily_data)
        
        if volume_profile:
            poc_price = volume_profile['poc_price']
            poc_distance = volume_profile['poc_distance']
            
            print(f"POC (최대 거래량 가격): ${poc_price:.2f}")
            print(f"POC 대비 현재가: {poc_distance:+.2f}%")
            
            # 상위 5개 매물대
            print("\n주요 매물대 (상위 5개):")
            for i, (price_bin, volume) in enumerate(volume_profile['volume_profile'].head(5).items()):
                avg_price = (price_bin.left + price_bin.right) / 2
                distance = (current_price - avg_price) / avg_price * 100
                print(f"  {i+1}. ${avg_price:.2f} (현재가 대비 {distance:+.2f}%) - 거래량: {volume:,.0f}")
    
    # 지지/저항선 분석
    if daily_data is not None and not daily_data.empty:
        print(f"\n📊 지지/저항선 분석:")
        sr_analysis = analyze_support_resistance(daily_data)
        
        if sr_analysis and not sr_analysis['resistance'].empty:
            print("주요 저항선:")
            for i, row in sr_analysis['resistance'].iterrows():
                distance = (row['price'] - current_price) / current_price * 100
                print(f"  ${row['price']:.2f} (+{distance:.2f}%) - {row['date'].strftime('%m-%d')}")
        
        if sr_analysis and not sr_analysis['support'].empty:
            print("주요 지지선:")
            for i, row in sr_analysis['support'].iterrows():
                distance = (current_price - row['price']) / row['price'] * 100
                print(f"  ${row['price']:.2f} (-{distance:.2f}%) - {row['date'].strftime('%m-%d')}")
    
    # 호가창 분석
    if orderbook:
        print(f"\n📋 호가창 분석:")
        
        bids = orderbook['bids'][:10]
        asks = orderbook['asks'][:10]
        
        total_bid_volume = sum([bid[1] for bid in bids])
        total_ask_volume = sum([ask[1] for ask in asks])
        
        print(f"매수 벽 (상위 10단계): {total_bid_volume:,.2f} ETH")
        print(f"매도 벽 (상위 10단계): {total_ask_volume:,.2f} ETH")
        print(f"매수/매도 비율: {total_bid_volume/total_ask_volume:.2f}")
        
        # 스프레드
        best_bid = bids[0][0]
        best_ask = asks[0][0]
        spread = best_ask - best_bid
        spread_pct = (spread / best_bid) * 100
        
        print(f"스프레드: ${spread:.2f} ({spread_pct:.3f}%)")
        
        if total_bid_volume > total_ask_volume * 1.2:
            print("  → 매수 압력 우세")
        elif total_ask_volume > total_bid_volume * 1.2:
            print("  → 매도 압력 우세")
        else:
            print("  → 균형 상태")
    
    # 최근 거래 분석
    if trades is not None and not trades.empty:
        print(f"\n💹 최근 거래 분석:")
        
        buy_trades = trades[trades['side'] == 'Buy']
        sell_trades = trades[trades['side'] == 'Sell']
        
        buy_volume = buy_trades['size'].sum()
        sell_volume = sell_trades['size'].sum()
        
        print(f"최근 50건 중 매수 거래량: {buy_volume:,.2f} ETH")
        print(f"최근 50건 중 매도 거래량: {sell_volume:,.2f} ETH")
        if sell_volume > 0:
            print(f"매수/매도 거래량 비율: {buy_volume/sell_volume:.2f}")
        
        # 대량 거래 분석
        large_trades = trades[trades['size'] > trades['size'].quantile(0.9)]
        if not large_trades.empty:
            print(f"대량 거래 (상위 10%): {len(large_trades)}건")
            avg_large_trade = large_trades['size'].mean()
            print(f"대량 거래 평균 크기: {avg_large_trade:,.2f} ETH")
    
    # 캔들 패턴 분석
    hourly_data = timeframes['1시간']
    if hourly_data is not None and not hourly_data.empty:
        print(f"\n🕯️ 최근 캔들 패턴 (1시간봉):")
        patterns = analyze_candle_patterns(hourly_data)
        
        if patterns:
            for pattern in patterns[-3:]:  # 최근 3개
                print(f"  {pattern['timestamp'].strftime('%m-%d %H:%M')}: {pattern['pattern']} "
                      f"(중요도: {pattern['significance']}/10)")
        else:
            print("  특별한 패턴이 감지되지 않았습니다.")
    
    # 종합 매매 신호
    print(f"\n⚡ 종합 매매 신호:")
    
    if daily_data is not None and indicators:
        signals, signal_strength = generate_trading_signals(
            ticker, indicators, orderbook, trades, 
            analyze_volume_profile(daily_data)
        )
        
        if signal_strength >= 2:
            overall_signal = "🟢 강한 매수 신호"
        elif signal_strength >= 1:
            overall_signal = "🔵 약한 매수 신호"
        elif signal_strength <= -2:
            overall_signal = "🔴 강한 매도 신호"
        elif signal_strength <= -1:
            overall_signal = "🟡 약한 매도 신호"
        else:
            overall_signal = "⚪ 중립 - 관망 권장"
        
        print(f"종합 신호: {overall_signal}")
        print(f"신호 강도: {signal_strength:.1f}")
        
        if signals:
            print("\n신호 근거:")
            for signal in signals:
                print(f"  • {signal}")
    
    print(f"\n{'='*60}")
    print("✅ 이더리움 분석 완료!")
    print(f"분석 시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
