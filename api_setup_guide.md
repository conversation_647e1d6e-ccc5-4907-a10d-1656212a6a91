# 🔑 키움증권 + 바이비트 API 설정 가이드

## 📈 키움증권 OpenAPI+ 설정

### 1. 계좌 개설 및 API 신청
1. **키움증권 계좌 개설**
   - 홈페이지: https://www.kiwoom.com
   - 실계좌 또는 모의투자 계좌 개설

2. **OpenAPI+ 신청**
   - 키움증권 홈페이지 → 트레이딩 → OpenAPI+
   - 신청서 작성 및 제출
   - 승인까지 1-2일 소요

3. **API Key 발급**
   - 승인 후 App Key, Secret Key 발급
   - 모의투자용과 실거래용 키가 별도

### 2. 개발 환경 설정
```bash
# 필요한 라이브러리 설치
pip install requests pandas numpy websocket-client
```

### 3. API 사용 제한사항
- **일일 호출 제한**: 10,000회
- **분당 호출 제한**: 200회
- **실시간 데이터**: WebSocket 연결 필요
- **장중 시간**: 09:00 ~ 15:30 (한국시간)

### 4. 주요 API 엔드포인트
```python
# 기본 URL
BASE_URL = "https://openapi.kiwoom.com"

# 주요 엔드포인트
ENDPOINTS = {
    'oauth': '/oauth2/tokenP',           # 토큰 발급
    'current_price': '/uapi/domestic-stock/v1/quotations/inquire-price',
    'chart_data': '/uapi/domestic-stock/v1/quotations/inquire-daily-itemchartprice',
    'orderbook': '/uapi/domestic-stock/v1/quotations/inquire-asking-price-exp-ccn'
}
```

## 🪙 바이비트(Bybit) API 설정

### 1. 계정 생성 및 API 키 발급
1. **바이비트 계정 생성**
   - 홈페이지: https://www.bybit.com
   - 이메일 인증 및 KYC 완료

2. **API 키 생성**
   - 계정 → API 관리 → API 키 생성
   - 권한 설정: 읽기 전용 (분석용)
   - IP 화이트리스트 설정 권장

3. **테스트넷 사용**
   - 테스트넷: https://testnet.bybit.com
   - 실제 자금 없이 API 테스트 가능

### 2. API 사용법
```python
# 바이비트 API 기본 설정
API_KEY = "your_api_key"
API_SECRET = "your_api_secret"
BASE_URL = "https://api.bybit.com"  # 메인넷
# BASE_URL = "https://api-testnet.bybit.com"  # 테스트넷
```

### 3. 주요 기능
- **실시간 가격 데이터**: 무료, 제한 없음
- **호가창 데이터**: 실시간 매수/매도 호가
- **거래 내역**: 최근 거래 기록
- **캔들 데이터**: 다양한 시간대 OHLCV

### 4. 지원 암호화폐
```python
POPULAR_SYMBOLS = [
    'BTCUSDT',   # 비트코인
    'ETHUSDT',   # 이더리움
    'ADAUSDT',   # 카르다노
    'SOLUSDT',   # 솔라나
    'DOTUSDT',   # 폴카닷
    'LINKUSDT',  # 체인링크
    'MATICUSDT', # 폴리곤
    'AVAXUSDT',  # 아발란체
]
```

## 🔧 통합 분석 시스템 사용법

### 1. 기본 실행
```bash
python kiwoom_bybit_analyzer.py
```

### 2. 한국 주식 분석 예시
```
📊 분석 옵션을 선택하세요:
1. 한국 주식 분석 (키움증권)

선택 (1-3): 1

App Key를 입력하세요: [키움증권 App Key]
Secret Key를 입력하세요: [키움증권 Secret Key]

한국 주식 코드 입력 (예: 005930): 005930

📊 005930 키움증권 분석
==================================================
💰 기본 정보:
종목명: 삼성전자
현재가: 71,000원
전일대비: +1,000원 (+1.43%)
거래량: 15,000,000주
```

### 3. 암호화폐 분석 예시
```
📊 분석 옵션을 선택하세요:
2. 암호화폐 분석 (바이비트)

선택 (1-3): 2

암호화폐 심볼 입력 (예: BTCUSDT): BTCUSDT

📊 BTCUSDT 바이비트 거래량 흐름 분석
==================================================
💰 기본 정보:
현재가: $43,250.50
24시간 변동: +$1,250.30 (+2.98%)
24시간 거래량: 1,250.45
24시간 거래대금: $54,125,000.00
```

## 📊 고급 분석 기능

### 1. 거래량 프로파일 분석
- **POC (Point of Control)**: 최대 거래량 발생 가격
- **매물대 분석**: 거래량 집중 구간 식별
- **현재가 vs POC**: 상대적 위치 분석

### 2. 호가창 분석 (바이비트)
- **매수/매도 벽**: 대량 호가 탐지
- **스프레드 분석**: 매수/매도 호가 차이
- **호가 불균형**: 매수/매도 압력 측정

### 3. 실시간 거래 분석
- **매수/매도 비율**: 최근 거래의 방향성
- **대량 거래 탐지**: 기관/고래 거래 추정
- **거래 강도**: 시간당 거래량 변화

### 4. 다중 시간대 신호
- **1분, 5분, 1시간, 4시간, 일봉** 동시 분석
- **시간대별 추세**: 단기/중기/장기 방향성
- **거래량 확인**: 각 시간대별 거래량 비교

## ⚠️ 주의사항

### 1. API 제한 준수
- **키움증권**: 일일 10,000회, 분당 200회 제한
- **바이비트**: 공개 API는 제한 없음, 개인 API는 제한 있음

### 2. 보안 관리
- **API 키 보안**: 코드에 직접 하드코딩 금지
- **환경 변수 사용**: `.env` 파일 또는 환경 변수로 관리
- **권한 최소화**: 읽기 전용 권한만 부여

### 3. 데이터 정확성
- **실시간 데이터**: 약간의 지연 가능성
- **휴장일 처리**: 한국 주식 휴장일 고려
- **시간대 차이**: UTC vs KST 시간 변환 주의

### 4. 투자 책임
- **분석 도구**: 투자 조언이 아닌 분석 도구
- **리스크 관리**: 반드시 손절매 설정
- **분산 투자**: 단일 종목 집중 투자 지양

## 🚀 다음 단계

1. **실시간 알림 시스템** 구축
2. **자동 매매 시스템** 연동
3. **포트폴리오 관리** 기능 추가
4. **백테스팅 시스템** 구현
5. **웹 대시보드** 개발

이 가이드를 따라 설정하면 키움증권과 바이비트의 실시간 데이터를 활용한 전문적인 분석이 가능합니다!
