"""
이더리움 실시간 정확한 가격 분석 ($4,200 근처)
Ethereum Real-time Accurate Price Analysis
"""

import urllib.request
import json
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_eth_realtime_data():
    """이더리움 실시간 데이터 수집"""
    print("🔷 이더리움 실시간 데이터 수집 중...")
    
    try:
        # 현재가 정보
        ticker_url = "https://api.bybit.com/v5/market/tickers?category=spot&symbol=ETHUSDT"
        with urllib.request.urlopen(ticker_url) as response:
            ticker_data = json.loads(response.read().decode())
        
        if ticker_data['retCode'] != 0:
            print("❌ 현재가 데이터 수집 실패")
            return None
        
        ticker = ticker_data['result']['list'][0]
        
        # 1시간봉 데이터 (최근 168시간 = 1주일)
        kline_1h_url = "https://api.bybit.com/v5/market/kline?category=spot&symbol=ETHUSDT&interval=60&limit=168"
        with urllib.request.urlopen(kline_1h_url) as response:
            kline_1h_data = json.loads(response.read().decode())
        
        # 4시간봉 데이터 (최근 42개 = 1주일)
        kline_4h_url = "https://api.bybit.com/v5/market/kline?category=spot&symbol=ETHUSDT&interval=240&limit=42"
        with urllib.request.urlopen(kline_4h_url) as response:
            kline_4h_data = json.loads(response.read().decode())
        
        # 일봉 데이터 (최근 30일)
        kline_1d_url = "https://api.bybit.com/v5/market/kline?category=spot&symbol=ETHUSDT&interval=D&limit=30"
        with urllib.request.urlopen(kline_1d_url) as response:
            kline_1d_data = json.loads(response.read().decode())
        
        # 호가창 데이터
        orderbook_url = "https://api.bybit.com/v5/market/orderbook?category=spot&symbol=ETHUSDT&limit=25"
        with urllib.request.urlopen(orderbook_url) as response:
            orderbook_data = json.loads(response.read().decode())
        
        # 최근 거래 데이터
        trades_url = "https://api.bybit.com/v5/market/recent-trade?category=spot&symbol=ETHUSDT&limit=100"
        with urllib.request.urlopen(trades_url) as response:
            trades_data = json.loads(response.read().decode())
        
        return {
            'ticker': ticker,
            'kline_1h': kline_1h_data['result']['list'] if kline_1h_data['retCode'] == 0 else [],
            'kline_4h': kline_4h_data['result']['list'] if kline_4h_data['retCode'] == 0 else [],
            'kline_1d': kline_1d_data['result']['list'] if kline_1d_data['retCode'] == 0 else [],
            'orderbook': orderbook_data['result'] if orderbook_data['retCode'] == 0 else None,
            'trades': trades_data['result']['list'] if trades_data['retCode'] == 0 else []
        }
        
    except Exception as e:
        print(f"❌ 데이터 수집 실패: {e}")
        return None

def analyze_current_price_action(ticker, kline_1h):
    """현재 가격 액션 분석"""
    current_price = float(ticker['lastPrice'])
    price_change_24h = float(ticker['price24hPcnt']) * 100
    volume_24h = float(ticker['volume24h'])
    high_24h = float(ticker['highPrice24h'])
    low_24h = float(ticker['lowPrice24h'])
    
    print(f"\n💰 이더리움 현재 상황 (실시간)")
    print(f"현재가: ${current_price:,.2f}")
    print(f"24시간 변동: {price_change_24h:+.2f}%")
    print(f"24시간 고가: ${high_24h:,.2f} (현재가 대비 {(high_24h-current_price)/current_price*100:+.2f}%)")
    print(f"24시간 저가: ${low_24h:,.2f} (현재가 대비 {(current_price-low_24h)/low_24h*100:+.2f}%)")
    print(f"24시간 거래량: {volume_24h:,.0f} ETH")
    
    # 24시간 범위 내 위치
    daily_range = high_24h - low_24h
    current_position = (current_price - low_24h) / daily_range
    
    print(f"\n📊 24시간 범위 내 위치: {current_position:.1%}")
    if current_position > 0.8:
        print("  → 24시간 고가 근처 (저항 구간)")
    elif current_position < 0.2:
        print("  → 24시간 저가 근처 (지지 구간)")
    else:
        print("  → 24시간 중간 구간")
    
    # 최근 1시간 움직임
    if kline_1h and len(kline_1h) >= 5:
        recent_candles = kline_1h[:5]  # 최근 5시간
        
        print(f"\n⏰ 최근 5시간 움직임:")
        for i, candle in enumerate(recent_candles):
            timestamp = datetime.fromtimestamp(int(candle[0])/1000)
            open_price = float(candle[1])
            close_price = float(candle[4])
            volume = float(candle[5])
            change = (close_price - open_price) / open_price * 100
            
            if change > 0:
                emoji = "🟢"
            elif change < 0:
                emoji = "🔴"
            else:
                emoji = "⚪"
            
            print(f"  {timestamp.strftime('%m-%d %H:%M')}: {emoji} ${close_price:,.2f} ({change:+.2f}%) Vol: {volume:,.0f}")

def analyze_key_levels(kline_1h, kline_4h, kline_1d, current_price):
    """주요 가격 레벨 분석"""
    print(f"\n🎯 주요 가격 레벨 분석 (현재가: ${current_price:,.2f})")
    
    # 1시간봉 기준 단기 지지/저항
    if kline_1h and len(kline_1h) >= 24:
        h1_highs = [float(candle[2]) for candle in kline_1h[:24]]  # 최근 24시간 고가들
        h1_lows = [float(candle[3]) for candle in kline_1h[:24]]   # 최근 24시간 저가들
        
        resistance_1h = max(h1_highs)
        support_1h = min(h1_lows)
        
        print(f"\n📈 단기 레벨 (24시간 기준):")
        print(f"저항선: ${resistance_1h:,.2f} ({(resistance_1h-current_price)/current_price*100:+.2f}%)")
        print(f"지지선: ${support_1h:,.2f} ({(current_price-support_1h)/support_1h*100:+.2f}%)")
    
    # 4시간봉 기준 중기 지지/저항
    if kline_4h and len(kline_4h) >= 21:  # 최근 3.5일
        h4_highs = [float(candle[2]) for candle in kline_4h[:21]]
        h4_lows = [float(candle[3]) for candle in kline_4h[:21]]
        
        resistance_4h = max(h4_highs)
        support_4h = min(h4_lows)
        
        print(f"\n📊 중기 레벨 (3.5일 기준):")
        print(f"저항선: ${resistance_4h:,.2f} ({(resistance_4h-current_price)/current_price*100:+.2f}%)")
        print(f"지지선: ${support_4h:,.2f} ({(current_price-support_4h)/support_4h*100:+.2f}%)")
    
    # 일봉 기준 장기 지지/저항
    if kline_1d and len(kline_1d) >= 30:
        d1_highs = [float(candle[2]) for candle in kline_1d[:30]]
        d1_lows = [float(candle[3]) for candle in kline_1d[:30]]
        
        resistance_1d = max(d1_highs)
        support_1d = min(d1_lows)
        
        print(f"\n📅 장기 레벨 (30일 기준):")
        print(f"저항선: ${resistance_1d:,.2f} ({(resistance_1d-current_price)/current_price*100:+.2f}%)")
        print(f"지지선: ${support_1d:,.2f} ({(current_price-support_1d)/support_1d*100:+.2f}%)")
    
    # 심리적 저항선 (라운드 넘버)
    print(f"\n🧠 심리적 저항선:")
    round_levels = [4000, 4100, 4200, 4300, 4400, 4500, 4600, 4700, 4800, 4900, 5000]
    
    for level in round_levels:
        if abs(current_price - level) / current_price < 0.05:  # 5% 이내
            distance = (level - current_price) / current_price * 100
            if distance > 0:
                print(f"  ${level:,} ({distance:+.2f}%) - 다음 저항")
            else:
                print(f"  ${level:,} ({distance:+.2f}%) - 최근 돌파한 저항")

def analyze_volume_and_momentum(kline_1h, kline_4h):
    """거래량 및 모멘텀 분석"""
    print(f"\n📊 거래량 및 모멘텀 분석")
    
    # 1시간봉 거래량 분석
    if kline_1h and len(kline_1h) >= 24:
        recent_volumes = [float(candle[5]) for candle in kline_1h[:24]]
        avg_volume_24h = sum(recent_volumes) / len(recent_volumes)
        current_volume = recent_volumes[0]
        
        print(f"\n⏰ 1시간봉 거래량:")
        print(f"현재 시간 거래량: {current_volume:,.0f} ETH")
        print(f"24시간 평균: {avg_volume_24h:,.0f} ETH")
        print(f"거래량 비율: {current_volume/avg_volume_24h:.2f}배")
        
        if current_volume > avg_volume_24h * 1.5:
            print("  → 높은 거래량 (관심 집중)")
        elif current_volume < avg_volume_24h * 0.5:
            print("  → 낮은 거래량 (관심 부족)")
        else:
            print("  → 평범한 거래량")
    
    # 4시간봉 모멘텀 분석
    if kline_4h and len(kline_4h) >= 6:
        print(f"\n🚀 4시간봉 모멘텀 (최근 24시간):")
        
        momentum_score = 0
        for i, candle in enumerate(kline_4h[:6]):
            open_price = float(candle[1])
            close_price = float(candle[4])
            change = (close_price - open_price) / open_price * 100
            volume = float(candle[5])
            timestamp = datetime.fromtimestamp(int(candle[0])/1000)
            
            if change > 0:
                emoji = "🟢"
                momentum_score += 1
            elif change < 0:
                emoji = "🔴"
                momentum_score -= 1
            else:
                emoji = "⚪"
            
            print(f"  {timestamp.strftime('%m-%d %H:%M')}: {emoji} {change:+.2f}% (Vol: {volume:,.0f})")
        
        print(f"\n모멘텀 점수: {momentum_score}/6")
        if momentum_score >= 3:
            print("  → 강한 상승 모멘텀")
        elif momentum_score <= -3:
            print("  → 강한 하락 모멘텀")
        else:
            print("  → 중립적 모멘텀")

def analyze_orderbook_realtime(orderbook, current_price):
    """실시간 호가창 분석"""
    if not orderbook:
        return
    
    print(f"\n📋 실시간 호가창 분석")
    
    bids = [[float(bid[0]), float(bid[1])] for bid in orderbook['b'][:15]]
    asks = [[float(ask[0]), float(ask[1])] for ask in orderbook['a'][:15]]
    
    # 상위 5개 매수/매도 호가
    print(f"\n매수 호가 (상위 5개):")
    for i, (price, size) in enumerate(bids[:5]):
        distance = (current_price - price) / price * 100
        print(f"  {i+1}. ${price:,.2f} ({distance:+.2f}%) - {size:.2f} ETH")
    
    print(f"\n매도 호가 (상위 5개):")
    for i, (price, size) in enumerate(asks[:5]):
        distance = (price - current_price) / current_price * 100
        print(f"  {i+1}. ${price:,.2f} (+{distance:.2f}%) - {size:.2f} ETH")
    
    # 매수/매도 벽 분석
    bid_wall_5 = sum([bid[1] for bid in bids[:5]])
    ask_wall_5 = sum([ask[1] for ask in asks[:5]])
    bid_wall_15 = sum([bid[1] for bid in bids[:15]])
    ask_wall_15 = sum([ask[1] for ask in asks[:15]])
    
    print(f"\n🏗️ 매수/매도 벽 분석:")
    print(f"매수 벽 (상위 5단계): {bid_wall_5:.2f} ETH")
    print(f"매도 벽 (상위 5단계): {ask_wall_5:.2f} ETH")
    print(f"매수 벽 (상위 15단계): {bid_wall_15:.2f} ETH")
    print(f"매도 벽 (상위 15단계): {ask_wall_15:.2f} ETH")
    
    ratio_5 = bid_wall_5 / ask_wall_5 if ask_wall_5 > 0 else 0
    ratio_15 = bid_wall_15 / ask_wall_15 if ask_wall_15 > 0 else 0
    
    print(f"매수/매도 비율 (5단계): {ratio_5:.2f}")
    print(f"매수/매도 비율 (15단계): {ratio_15:.2f}")
    
    if ratio_5 > 1.5:
        print("  → 강한 매수 압력")
    elif ratio_5 < 0.67:
        print("  → 강한 매도 압력")
    else:
        print("  → 균형 상태")
    
    # 스프레드 분석
    best_bid = bids[0][0]
    best_ask = asks[0][0]
    spread = best_ask - best_bid
    spread_pct = (spread / best_bid) * 100
    
    print(f"\n💰 스프레드: ${spread:.2f} ({spread_pct:.3f}%)")
    if spread_pct < 0.01:
        print("  → 매우 높은 유동성")
    elif spread_pct < 0.05:
        print("  → 높은 유동성")
    else:
        print("  → 보통 유동성")

def analyze_recent_trades(trades, current_price):
    """최근 거래 분석"""
    if not trades:
        return
    
    print(f"\n💹 최근 거래 분석 (100건)")
    
    buy_volume = 0
    sell_volume = 0
    buy_count = 0
    sell_count = 0
    large_trades = []
    
    for trade in trades:
        price = float(trade['price'])
        size = float(trade['size'])
        side = trade['side']
        
        if side == 'Buy':
            buy_volume += size
            buy_count += 1
        else:
            sell_volume += size
            sell_count += 1
        
        # 대량 거래 (1 ETH 이상)
        if size >= 1.0:
            timestamp = datetime.fromtimestamp(int(trade['time'])/1000)
            large_trades.append({
                'time': timestamp,
                'price': price,
                'size': size,
                'side': side
            })
    
    total_volume = buy_volume + sell_volume
    
    print(f"총 거래량: {total_volume:.2f} ETH")
    print(f"매수 거래량: {buy_volume:.2f} ETH ({buy_volume/total_volume*100:.1f}%)")
    print(f"매도 거래량: {sell_volume:.2f} ETH ({sell_volume/total_volume*100:.1f}%)")
    print(f"매수/매도 비율: {buy_volume/sell_volume:.2f}" if sell_volume > 0 else "매수/매도 비율: 무한대")
    
    print(f"\n거래 건수:")
    print(f"매수 거래: {buy_count}건 ({buy_count/(buy_count+sell_count)*100:.1f}%)")
    print(f"매도 거래: {sell_count}건 ({sell_count/(buy_count+sell_count)*100:.1f}%)")
    
    if buy_volume > sell_volume * 1.2:
        print("  → 매수 압력 우세")
    elif sell_volume > buy_volume * 1.2:
        print("  → 매도 압력 우세")
    else:
        print("  → 균형 상태")
    
    # 대량 거래 분석
    if large_trades:
        print(f"\n🐋 대량 거래 (1 ETH 이상): {len(large_trades)}건")
        
        large_buy = [t for t in large_trades if t['side'] == 'Buy']
        large_sell = [t for t in large_trades if t['side'] == 'Sell']
        
        print(f"대량 매수: {len(large_buy)}건")
        print(f"대량 매도: {len(large_sell)}건")
        
        # 최근 5개 대량 거래
        if len(large_trades) > 0:
            print(f"\n최근 대량 거래 (상위 5개):")
            for i, trade in enumerate(large_trades[:5]):
                emoji = "🟢" if trade['side'] == 'Buy' else "🔴"
                print(f"  {i+1}. {emoji} ${trade['price']:,.2f} - {trade['size']:.2f} ETH ({trade['time'].strftime('%H:%M:%S')})")

def generate_trading_recommendation(data):
    """매매 추천 생성"""
    ticker = data['ticker']
    current_price = float(ticker['lastPrice'])
    price_change_24h = float(ticker['price24hPcnt']) * 100
    
    print(f"\n⚡ 매매 추천 (현재가: ${current_price:,.2f})")
    print("=" * 50)
    
    # 점수 시스템
    score = 0
    reasons = []
    
    # 1. 24시간 변동률
    if price_change_24h > 5:
        score += 1
        reasons.append(f"강한 상승 모멘텀 (+{price_change_24h:.2f}%)")
    elif price_change_24h > 2:
        score += 0.5
        reasons.append(f"상승 모멘텀 (+{price_change_24h:.2f}%)")
    elif price_change_24h < -5:
        score -= 1
        reasons.append(f"강한 하락 모멘텀 ({price_change_24h:.2f}%)")
    elif price_change_24h < -2:
        score -= 0.5
        reasons.append(f"하락 모멘텀 ({price_change_24h:.2f}%)")
    
    # 2. 현재가 위치 (4200 기준)
    if current_price > 4250:
        score -= 0.5
        reasons.append("고가 구간 (조정 위험)")
    elif current_price < 4150:
        score += 0.5
        reasons.append("상대적 저가 구간")
    
    # 3. 심리적 저항선 근접도
    key_levels = [4200, 4300, 4400, 4500]
    for level in key_levels:
        if abs(current_price - level) / current_price < 0.02:  # 2% 이내
            if current_price < level:
                score -= 0.3
                reasons.append(f"${level} 저항선 근접")
            break
    
    # 최종 추천
    if score >= 1.5:
        recommendation = "🟢 강한 매수"
    elif score >= 0.5:
        recommendation = "🔵 약한 매수"
    elif score <= -1.5:
        recommendation = "🔴 강한 매도"
    elif score <= -0.5:
        recommendation = "🟡 약한 매도"
    else:
        recommendation = "⚪ 중립 (관망)"
    
    print(f"추천: {recommendation}")
    print(f"신뢰도: {abs(score)*20:.0f}%")
    print(f"점수: {score:.1f}")
    
    print(f"\n근거:")
    for reason in reasons:
        print(f"  • {reason}")
    
    # 구체적 전략
    print(f"\n📋 구체적 전략:")
    
    if "매수" in recommendation:
        print(f"💰 매수 전략:")
        print(f"  - 진입가: ${current_price-20:.0f} ~ ${current_price+20:.0f}")
        print(f"  - 목표가 1: ${current_price*1.03:.0f} (3% 수익)")
        print(f"  - 목표가 2: ${current_price*1.05:.0f} (5% 수익)")
        print(f"  - 손절가: ${current_price*0.97:.0f} (3% 손실)")
    
    elif "매도" in recommendation:
        print(f"💸 매도 전략:")
        print(f"  - 매도가: ${current_price-10:.0f} ~ ${current_price+10:.0f}")
        print(f"  - 재진입 1: ${current_price*0.97:.0f} (3% 하락)")
        print(f"  - 재진입 2: ${current_price*0.95:.0f} (5% 하락)")
    
    else:
        print(f"⏳ 관망 전략:")
        print(f"  - 매수 대기: ${current_price*0.98:.0f} 이하")
        print(f"  - 매도 고려: ${current_price*1.02:.0f} 이상")
        print(f"  - 변동성 대기 중")

def main():
    """메인 분석 함수"""
    print("🔷 이더리움 실시간 정밀 분석 ($4,200 근처)")
    print("=" * 60)
    
    # 실시간 데이터 수집
    data = get_eth_realtime_data()
    if not data:
        print("❌ 데이터 수집 실패")
        return
    
    current_price = float(data['ticker']['lastPrice'])
    
    # 각종 분석 실행
    analyze_current_price_action(data['ticker'], data['kline_1h'])
    
    analyze_key_levels(data['kline_1h'], data['kline_4h'], data['kline_1d'], current_price)
    
    analyze_volume_and_momentum(data['kline_1h'], data['kline_4h'])
    
    analyze_orderbook_realtime(data['orderbook'], current_price)
    
    analyze_recent_trades(data['trades'], current_price)
    
    generate_trading_recommendation(data)
    
    print(f"\n{'='*60}")
    print(f"✅ 이더리움 실시간 분석 완료!")
    print(f"분석 시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"현재가: ${current_price:,.2f}")

if __name__ == "__main__":
    main()
