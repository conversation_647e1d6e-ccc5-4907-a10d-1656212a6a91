"""
간단한 테스트 버전 - 거래량 및 매물대 분석
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_stock(symbol):
    """종목 분석 함수"""
    print(f"\n📊 {symbol} 분석 시작...")
    
    try:
        # 데이터 수집
        ticker = yf.Ticker(symbol)
        
        # 다양한 시간대 데이터
        data_1h = ticker.history(period="5d", interval="1h")
        data_4h = ticker.history(period="1mo", interval="4h") 
        data_1d = ticker.history(period="3mo", interval="1d")
        
        if data_1d.empty:
            print(f"❌ {symbol} 데이터를 가져올 수 없습니다.")
            return
        
        print(f"✅ 데이터 수집 완료:")
        print(f"  - 1시간봉: {len(data_1h)}개")
        print(f"  - 4시간봉: {len(data_4h)}개") 
        print(f"  - 일봉: {len(data_1d)}개")
        
        # 현재 정보
        current_price = data_1d['Close'].iloc[-1]
        current_volume = data_1d['Volume'].iloc[-1]
        avg_volume = data_1d['Volume'].rolling(20).mean().iloc[-1]
        
        print(f"\n💰 현재 정보:")
        print(f"현재가: ${current_price:.2f}")
        print(f"현재 거래량: {current_volume:,.0f}")
        print(f"평균 거래량: {avg_volume:,.0f}")
        print(f"거래량 비율: {current_volume/avg_volume:.2f}배")
        
        # 거래량 프로파일 분석
        print(f"\n🎯 매물대 분석:")
        analyze_volume_profile(data_1d)
        
        # 기술적 지표
        print(f"\n📈 기술적 지표:")
        analyze_technical_indicators(data_1d)
        
        # 캔들 패턴 (1시간봉)
        if not data_1h.empty:
            print(f"\n🕯️ 최근 캔들 패턴 (1시간봉):")
            analyze_candle_patterns(data_1h)
        
        # 매매 신호
        print(f"\n⚡ 매매 신호:")
        generate_signals(data_1d, data_4h, data_1h)
        
    except Exception as e:
        print(f"❌ 분석 중 오류 발생: {e}")

def analyze_volume_profile(data, bins=20):
    """거래량 프로파일 분석"""
    price_min = data['Low'].min()
    price_max = data['High'].max()
    price_bins = np.linspace(price_min, price_max, bins)
    
    volume_profile = []
    
    for i in range(len(price_bins)-1):
        bin_low = price_bins[i]
        bin_high = price_bins[i+1]
        bin_center = (bin_low + bin_high) / 2
        
        # 해당 가격대 거래량
        mask = (data['Low'] <= bin_high) & (data['High'] >= bin_low)
        total_volume = data[mask]['Volume'].sum()
        
        volume_profile.append({
            'price': bin_center,
            'volume': total_volume
        })
    
    volume_df = pd.DataFrame(volume_profile)
    volume_df = volume_df.sort_values('volume', ascending=False)
    
    # POC (Point of Control)
    poc = volume_df.iloc[0]['price']
    current_price = data['Close'].iloc[-1]
    
    print(f"POC (최대 거래량 가격): ${poc:.2f}")
    print(f"POC 대비 현재가: {((current_price - poc) / poc * 100):+.2f}%")
    
    print("주요 매물대 (상위 3개):")
    for i, level in volume_df.head(3).iterrows():
        distance = ((current_price - level['price']) / level['price'] * 100)
        print(f"  ${level['price']:.2f} (현재가 대비 {distance:+.2f}%) - 거래량: {level['volume']:,.0f}")

def analyze_technical_indicators(data):
    """기술적 지표 분석"""
    # RSI 계산
    delta = data['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    # 볼린저 밴드
    bb_middle = data['Close'].rolling(20).mean()
    bb_std = data['Close'].rolling(20).std()
    bb_upper = bb_middle + (bb_std * 2)
    bb_lower = bb_middle - (bb_std * 2)
    
    # VWAP
    typical_price = (data['High'] + data['Low'] + data['Close']) / 3
    volume_price = typical_price * data['Volume']
    vwap = volume_price.rolling(20).sum() / data['Volume'].rolling(20).sum()
    
    current_price = data['Close'].iloc[-1]
    current_rsi = rsi.iloc[-1]
    current_bb_upper = bb_upper.iloc[-1]
    current_bb_lower = bb_lower.iloc[-1]
    current_vwap = vwap.iloc[-1]
    
    print(f"RSI: {current_rsi:.1f}", end="")
    if current_rsi > 70:
        print(" (과매수)")
    elif current_rsi < 30:
        print(" (과매도)")
    else:
        print(" (중립)")
    
    print(f"볼린저 밴드: ${current_bb_lower:.2f} ~ ${current_bb_upper:.2f}")
    if current_price > current_bb_upper:
        print("  → 상단 돌파")
    elif current_price < current_bb_lower:
        print("  → 하단 터치")
    else:
        print("  → 밴드 내부")
    
    print(f"VWAP: ${current_vwap:.2f}")
    print(f"현재가 vs VWAP: {((current_price - current_vwap) / current_vwap * 100):+.2f}%")

def analyze_candle_patterns(data):
    """캔들 패턴 분석"""
    recent_data = data.tail(10)  # 최근 10개 캔들
    
    patterns_found = []
    
    for i in range(1, len(recent_data)):
        current = recent_data.iloc[i]
        prev = recent_data.iloc[i-1]
        
        # 캔들 특성 계산
        body_size = abs(current['Close'] - current['Open'])
        total_range = current['High'] - current['Low']
        upper_shadow = current['High'] - max(current['Open'], current['Close'])
        lower_shadow = min(current['Open'], current['Close']) - current['Low']
        
        if total_range == 0:
            continue
            
        body_ratio = body_size / total_range
        
        # 패턴 식별
        pattern = None
        
        # 도지
        if body_ratio < 0.1:
            pattern = "DOJI"
        
        # 해머
        elif (lower_shadow > body_size * 2 and upper_shadow < body_size * 0.5):
            if current['Close'] > current['Open']:
                pattern = "HAMMER"
            else:
                pattern = "HANGING_MAN"
        
        # 유성
        elif (upper_shadow > body_size * 2 and lower_shadow < body_size * 0.5):
            pattern = "SHOOTING_STAR"
        
        # 엔걸핑
        elif (body_size > abs(prev['Close'] - prev['Open']) * 1.5):
            if (current['Open'] < prev['Close'] and current['Close'] > prev['Open']):
                pattern = "BULLISH_ENGULFING"
            elif (current['Open'] > prev['Close'] and current['Close'] < prev['Open']):
                pattern = "BEARISH_ENGULFING"
        
        if pattern:
            patterns_found.append({
                'time': current.name.strftime('%m-%d %H:%M'),
                'pattern': pattern,
                'price': current['Close']
            })
    
    if patterns_found:
        for p in patterns_found[-3:]:  # 최근 3개만 표시
            print(f"  {p['time']}: {p['pattern']} @ ${p['price']:.2f}")
    else:
        print("  특별한 패턴이 감지되지 않았습니다.")

def generate_signals(data_1d, data_4h, data_1h):
    """매매 신호 생성"""
    signals = []
    
    # 일봉 신호
    if not data_1d.empty:
        current = data_1d.iloc[-1]
        
        # RSI
        delta = data_1d['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1]
        
        if current_rsi < 30:
            signals.append("일봉 RSI 과매도 → 매수 고려")
        elif current_rsi > 70:
            signals.append("일봉 RSI 과매수 → 매도 고려")
        
        # 거래량
        avg_volume = data_1d['Volume'].rolling(20).mean().iloc[-1]
        if current['Volume'] > avg_volume * 1.5:
            signals.append("높은 거래량 → 관심 증가")
    
    # 4시간봉 신호
    if not data_4h.empty and len(data_4h) > 20:
        sma_20 = data_4h['Close'].rolling(20).mean().iloc[-1]
        current_price = data_4h['Close'].iloc[-1]
        
        if current_price > sma_20:
            signals.append("4시간봉 이평선 상향 → 상승 추세")
        else:
            signals.append("4시간봉 이평선 하향 → 하락 추세")
    
    # 1시간봉 신호
    if not data_1h.empty and len(data_1h) > 10:
        recent_high = data_1h['High'].rolling(10).max().iloc[-1]
        recent_low = data_1h['Low'].rolling(10).min().iloc[-1]
        current_price = data_1h['Close'].iloc[-1]
        
        if current_price >= recent_high * 0.99:
            signals.append("1시간봉 고점 근접 → 저항 확인 필요")
        elif current_price <= recent_low * 1.01:
            signals.append("1시간봉 저점 근접 → 지지 확인 필요")
    
    if signals:
        for signal in signals:
            print(f"  • {signal}")
    else:
        print("  특별한 신호가 없습니다.")

def main():
    """메인 함수"""
    print("🚀 거래량 및 매물대 분석 시스템 (테스트 버전)")
    print("=" * 60)
    print("지원 종목 예시:")
    print("  미국: AAPL, TSLA, NVDA, QQQ, SPY, MSFT")
    print("  한국: 005930.KS, 000660.KS, 035420.KS")  
    print("  코인: BTC-USD, ETH-USD, ADA-USD")
    print("=" * 60)
    
    while True:
        symbol = input("\n분석할 종목 코드 입력 (종료: quit): ").strip().upper()
        
        if symbol.lower() == 'quit':
            print("프로그램을 종료합니다.")
            break
        
        if not symbol:
            print("올바른 종목 코드를 입력해주세요.")
            continue
        
        analyze_stock(symbol)
        print("\n" + "="*60)

if __name__ == "__main__":
    main()
