"""
LSTM Nasdaq 매매 전략 분석 및 개선안
Enhanced Trading Strategy based on LSTM Nasdaq Prediction Model
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

class TradingStrategyAnalyzer:
    def __init__(self):
        self.signals = []
        self.positions = []
        self.returns = []
        
    def calculate_trading_signals(self, predictions, actual_prices, threshold=0.02):
        """
        예측값을 기반으로 매매 신호 생성
        
        Args:
            predictions: 모델 예측값
            actual_prices: 실제 가격
            threshold: 매매 신호 임계값 (2% 기본)
        
        Returns:
            signals: 1(매수), 0(보유), -1(매도)
        """
        signals = []
        
        for i in range(len(predictions)):
            if i == 0:
                signals.append(0)
                continue
                
            # 예측 수익률 계산
            predicted_return = (predictions[i] - actual_prices[i-1]) / actual_prices[i-1]
            
            if predicted_return > threshold:
                signals.append(1)  # 매수 신호
            elif predicted_return < -threshold:
                signals.append(-1)  # 매도 신호
            else:
                signals.append(0)  # 보유
                
        return np.array(signals)
    
    def backtest_strategy(self, prices, signals, initial_capital=100000):
        """
        백테스팅 수행
        
        Args:
            prices: 실제 가격 데이터
            signals: 매매 신호
            initial_capital: 초기 자본
            
        Returns:
            portfolio_value: 포트폴리오 가치 변화
            trades: 거래 내역
        """
        portfolio_value = [initial_capital]
        cash = initial_capital
        position = 0
        trades = []
        
        for i in range(1, len(prices)):
            current_price = prices[i]
            signal = signals[i]
            
            if signal == 1 and position == 0:  # 매수
                shares = cash // current_price
                if shares > 0:
                    position = shares
                    cash -= shares * current_price
                    trades.append(('BUY', i, current_price, shares))
                    
            elif signal == -1 and position > 0:  # 매도
                cash += position * current_price
                trades.append(('SELL', i, current_price, position))
                position = 0
            
            # 포트폴리오 가치 계산
            total_value = cash + position * current_price
            portfolio_value.append(total_value)
            
        return np.array(portfolio_value), trades
    
    def calculate_performance_metrics(self, portfolio_values, benchmark_prices):
        """
        성과 지표 계산
        """
        # 수익률 계산
        portfolio_returns = np.diff(portfolio_values) / portfolio_values[:-1]
        benchmark_returns = np.diff(benchmark_prices) / benchmark_prices[:-1]
        
        # 총 수익률
        total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]
        benchmark_total_return = (benchmark_prices[-1] - benchmark_prices[0]) / benchmark_prices[0]
        
        # 샤프 비율 (연율화)
        sharpe_ratio = np.sqrt(252) * np.mean(portfolio_returns) / np.std(portfolio_returns) if np.std(portfolio_returns) > 0 else 0
        
        # 최대 낙폭 (Maximum Drawdown)
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown)
        
        # 승률 계산
        win_rate = np.sum(portfolio_returns > 0) / len(portfolio_returns) if len(portfolio_returns) > 0 else 0
        
        return {
            'total_return': total_return,
            'benchmark_return': benchmark_total_return,
            'excess_return': total_return - benchmark_total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'volatility': np.std(portfolio_returns) * np.sqrt(252)
        }

def enhanced_feature_engineering(data):
    """
    추가 기술적 지표 생성으로 모델 성능 향상
    """
    # 볼린저 밴드
    data['BB_upper'] = data['Close'].rolling(20).mean() + 2 * data['Close'].rolling(20).std()
    data['BB_lower'] = data['Close'].rolling(20).mean() - 2 * data['Close'].rolling(20).std()
    data['BB_position'] = (data['Close'] - data['BB_lower']) / (data['BB_upper'] - data['BB_lower'])
    
    # MACD
    exp1 = data['Close'].ewm(span=12).mean()
    exp2 = data['Close'].ewm(span=26).mean()
    data['MACD'] = exp1 - exp2
    data['MACD_signal'] = data['MACD'].ewm(span=9).mean()
    data['MACD_histogram'] = data['MACD'] - data['MACD_signal']
    
    # 거래량 지표
    data['Volume_SMA'] = data['Volume'].rolling(20).mean()
    data['Volume_ratio'] = data['Volume'] / data['Volume_SMA']
    
    # 가격 모멘텀
    data['Price_momentum_5'] = data['Close'] / data['Close'].shift(5) - 1
    data['Price_momentum_10'] = data['Close'] / data['Close'].shift(10) - 1
    
    # 변동성 지표
    data['Volatility'] = data['Close'].rolling(20).std() / data['Close'].rolling(20).mean()
    
    return data

def risk_management_rules():
    """
    리스크 관리 규칙 정의
    """
    rules = {
        'max_position_size': 0.1,  # 최대 포지션 크기 (포트폴리오의 10%)
        'stop_loss': 0.05,         # 손절매 (5% 손실)
        'take_profit': 0.15,       # 익절 (15% 수익)
        'max_drawdown_limit': 0.15, # 최대 낙폭 제한 (15%)
        'correlation_threshold': 0.7, # 상관관계 임계값
        'volatility_filter': 0.3,   # 변동성 필터 (30% 이상 시 거래 중단)
    }
    return rules

def market_regime_detection(data, window=60):
    """
    시장 상황 감지 (상승장/하락장/횡보장)
    """
    # 이동평균 기울기로 추세 판단
    ma_slope = data['Close'].rolling(window).mean().diff()
    volatility = data['Close'].rolling(window).std()
    
    # 시장 상황 분류
    regime = []
    for i in range(len(data)):
        if i < window:
            regime.append('NEUTRAL')
        else:
            slope = ma_slope.iloc[i]
            vol = volatility.iloc[i]
            
            if slope > 0 and vol < volatility.median():
                regime.append('BULL')      # 상승장
            elif slope < 0 and vol < volatility.median():
                regime.append('BEAR')      # 하락장
            else:
                regime.append('SIDEWAYS')  # 횡보장
    
    return regime

def adaptive_strategy_parameters(regime):
    """
    시장 상황에 따른 적응형 전략 파라미터
    """
    if regime == 'BULL':
        return {'threshold': 0.015, 'holding_period': 5}
    elif regime == 'BEAR':
        return {'threshold': 0.025, 'holding_period': 3}
    else:  # SIDEWAYS
        return {'threshold': 0.02, 'holding_period': 2}

def portfolio_optimization_suggestions():
    """
    포트폴리오 최적화 제안사항
    """
    suggestions = """
    ## 포트폴리오 최적화 제안사항
    
    ### 1. 다중 자산 포트폴리오
    - QQQ 외에 SPY, IWM, TLT 등 추가
    - 섹터별 ETF 분산 투자
    - 국제 시장 ETF 포함
    
    ### 2. 동적 포지션 사이징
    - Kelly Criterion 활용
    - 변동성 기반 포지션 조정
    - 상관관계 고려한 리스크 패리티
    
    ### 3. 앙상블 모델
    - LSTM + Random Forest
    - LSTM + XGBoost
    - 다중 시간 프레임 예측
    
    ### 4. 실시간 모니터링
    - 모델 성능 추적
    - 드리프트 감지
    - 자동 재학습 시스템
    """
    return suggestions

if __name__ == "__main__":
    print("LSTM Nasdaq 매매 전략 분석 도구")
    print("=" * 50)
    
    # 사용 예시
    analyzer = TradingStrategyAnalyzer()
    
    # 샘플 데이터로 테스트
    np.random.seed(42)
    sample_prices = 100 + np.cumsum(np.random.randn(100) * 0.02)
    sample_predictions = sample_prices + np.random.randn(100) * 0.5
    
    # 매매 신호 생성
    signals = analyzer.calculate_trading_signals(sample_predictions, sample_prices)
    
    # 백테스팅
    portfolio_values, trades = analyzer.backtest_strategy(sample_prices, signals)
    
    # 성과 분석
    metrics = analyzer.calculate_performance_metrics(portfolio_values, sample_prices)
    
    print("성과 지표:")
    for key, value in metrics.items():
        print(f"{key}: {value:.4f}")
    
    print("\n" + portfolio_optimization_suggestions())
