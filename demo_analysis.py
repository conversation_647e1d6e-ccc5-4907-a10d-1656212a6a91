"""
데모 분석 - 샘플 데이터로 시스템 기능 시연
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def create_sample_data():
    """샘플 주가 데이터 생성"""
    np.random.seed(42)
    
    # 90일간의 일봉 데이터 생성
    dates = pd.date_range(start='2024-10-01', end='2025-01-09', freq='D')
    dates = dates[dates.weekday < 5]  # 주말 제외
    
    # 기본 가격 트렌드 (상승 추세)
    base_price = 180
    trend = np.linspace(0, 25, len(dates))  # 25달러 상승 트렌드
    
    # 랜덤 변동성 추가
    volatility = np.random.normal(0, 2, len(dates))
    
    # 가격 데이터 생성
    prices = base_price + trend + volatility.cumsum()
    
    data = []
    for i, date in enumerate(dates):
        open_price = prices[i] + np.random.normal(0, 0.5)
        close_price = prices[i] + np.random.normal(0, 0.5)
        
        # High/Low 계산
        high_price = max(open_price, close_price) + abs(np.random.normal(0, 1))
        low_price = min(open_price, close_price) - abs(np.random.normal(0, 1))
        
        # 거래량 (가격 변동과 상관관계)
        price_change = abs(close_price - open_price)
        base_volume = 50000000  # 5천만주
        volume_multiplier = 1 + price_change * 0.1 + np.random.normal(0, 0.3)
        volume = int(base_volume * max(0.3, volume_multiplier))
        
        data.append({
            'Date': date,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('Date', inplace=True)
    return df

def analyze_volume_profile_demo(data, bins=20):
    """거래량 프로파일 분석 데모"""
    print("🎯 매물대 분석 (거래량 프로파일)")
    
    price_min = data['Low'].min()
    price_max = data['High'].max()
    price_bins = np.linspace(price_min, price_max, bins)
    
    volume_profile = []
    
    for i in range(len(price_bins)-1):
        bin_low = price_bins[i]
        bin_high = price_bins[i+1]
        bin_center = (bin_low + bin_high) / 2
        
        # 해당 가격대 거래량 계산
        mask = (data['Low'] <= bin_high) & (data['High'] >= bin_low)
        total_volume = data[mask]['Volume'].sum()
        
        volume_profile.append({
            'price': bin_center,
            'volume': total_volume
        })
    
    volume_df = pd.DataFrame(volume_profile)
    volume_df = volume_df.sort_values('volume', ascending=False)
    
    # POC (Point of Control)
    poc = volume_df.iloc[0]['price']
    current_price = data['Close'].iloc[-1]
    
    print(f"현재가: ${current_price:.2f}")
    print(f"POC (최대 거래량 가격): ${poc:.2f}")
    print(f"POC 대비 현재가: {((current_price - poc) / poc * 100):+.2f}%")
    
    print("\n주요 매물대 (상위 5개):")
    for i, level in volume_df.head(5).iterrows():
        distance = ((current_price - level['price']) / level['price'] * 100)
        print(f"  ${level['price']:.2f} (현재가 대비 {distance:+.2f}%) - 거래량: {level['volume']:,.0f}")
    
    return volume_df

def find_support_resistance_demo(data):
    """지지/저항선 분석 데모"""
    print("\n📈 지지/저항선 분석")
    
    # 최근 20일 고점/저점 찾기
    window = 10
    highs = data['High'].rolling(window=window, center=True).max()
    lows = data['Low'].rolling(window=window, center=True).min()
    
    resistance_levels = []
    support_levels = []
    
    for i in range(window, len(data) - window):
        # 저항선 (고점)
        if data['High'].iloc[i] == highs.iloc[i]:
            resistance_levels.append({
                'price': data['High'].iloc[i],
                'date': data.index[i],
                'volume': data['Volume'].iloc[i]
            })
        
        # 지지선 (저점)
        if data['Low'].iloc[i] == lows.iloc[i]:
            support_levels.append({
                'price': data['Low'].iloc[i],
                'date': data.index[i],
                'volume': data['Volume'].iloc[i]
            })
    
    current_price = data['Close'].iloc[-1]
    
    # 최근 저항선
    resistance_df = pd.DataFrame(resistance_levels)
    if not resistance_df.empty:
        recent_resistance = resistance_df.tail(3)
        print("주요 저항선:")
        for i, level in recent_resistance.iterrows():
            distance = ((level['price'] - current_price) / current_price * 100)
            print(f"  ${level['price']:.2f} (+{distance:.2f}%) - {level['date'].strftime('%m-%d')}")
    
    # 최근 지지선
    support_df = pd.DataFrame(support_levels)
    if not support_df.empty:
        recent_support = support_df.tail(3)
        print("주요 지지선:")
        for i, level in recent_support.iterrows():
            distance = ((current_price - level['price']) / level['price'] * 100)
            print(f"  ${level['price']:.2f} (-{distance:.2f}%) - {level['date'].strftime('%m-%d')}")

def analyze_technical_indicators_demo(data):
    """기술적 지표 분석 데모"""
    print("\n📊 기술적 지표 분석")
    
    # RSI 계산
    delta = data['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    # 볼린저 밴드
    bb_middle = data['Close'].rolling(20).mean()
    bb_std = data['Close'].rolling(20).std()
    bb_upper = bb_middle + (bb_std * 2)
    bb_lower = bb_middle - (bb_std * 2)
    
    # VWAP
    typical_price = (data['High'] + data['Low'] + data['Close']) / 3
    volume_price = typical_price * data['Volume']
    cumulative_volume = data['Volume'].cumsum()
    cumulative_volume_price = volume_price.cumsum()
    vwap = cumulative_volume_price / cumulative_volume
    
    # 현재 값들
    current_price = data['Close'].iloc[-1]
    current_rsi = rsi.iloc[-1]
    current_bb_upper = bb_upper.iloc[-1]
    current_bb_lower = bb_lower.iloc[-1]
    current_vwap = vwap.iloc[-1]
    current_volume = data['Volume'].iloc[-1]
    avg_volume = data['Volume'].rolling(20).mean().iloc[-1]
    
    print(f"RSI: {current_rsi:.1f}", end="")
    if current_rsi > 70:
        print(" (과매수 구간)")
    elif current_rsi < 30:
        print(" (과매도 구간)")
    else:
        print(" (중립 구간)")
    
    print(f"볼린저 밴드: ${current_bb_lower:.2f} ~ ${current_bb_upper:.2f}")
    bb_position = (current_price - current_bb_lower) / (current_bb_upper - current_bb_lower)
    print(f"  → 밴드 내 위치: {bb_position:.1%}")
    
    print(f"VWAP: ${current_vwap:.2f}")
    vwap_diff = ((current_price - current_vwap) / current_vwap * 100)
    print(f"  → 현재가 vs VWAP: {vwap_diff:+.2f}%")
    
    print(f"거래량: {current_volume:,.0f} (평균 대비 {current_volume/avg_volume:.2f}배)")

def analyze_money_flow_demo(data):
    """자금 흐름 분석 데모"""
    print("\n💰 자금 흐름 분석")
    
    # Money Flow Index (MFI) 계산
    typical_price = (data['High'] + data['Low'] + data['Close']) / 3
    raw_money_flow = typical_price * data['Volume']
    
    positive_flow = []
    negative_flow = []
    
    for i in range(1, len(data)):
        if typical_price.iloc[i] > typical_price.iloc[i-1]:
            positive_flow.append(raw_money_flow.iloc[i])
            negative_flow.append(0)
        elif typical_price.iloc[i] < typical_price.iloc[i-1]:
            positive_flow.append(0)
            negative_flow.append(raw_money_flow.iloc[i])
        else:
            positive_flow.append(0)
            negative_flow.append(0)
    
    # 14일 MFI
    period = 14
    if len(positive_flow) >= period:
        positive_sum = sum(positive_flow[-period:])
        negative_sum = sum(negative_flow[-period:])
        
        if negative_sum > 0:
            money_ratio = positive_sum / negative_sum
            mfi = 100 - (100 / (1 + money_ratio))
        else:
            mfi = 100
        
        print(f"MFI (자금흐름지수): {mfi:.1f}")
        
        if mfi > 80:
            print("  → 과매수 상태 (매도 압력 증가 가능)")
        elif mfi < 20:
            print("  → 과매도 상태 (매수 기회 가능)")
        else:
            print("  → 중립 상태")
    
    # OBV (On Balance Volume)
    obv = 0
    obv_values = [0]
    
    for i in range(1, len(data)):
        if data['Close'].iloc[i] > data['Close'].iloc[i-1]:
            obv += data['Volume'].iloc[i]
        elif data['Close'].iloc[i] < data['Close'].iloc[i-1]:
            obv -= data['Volume'].iloc[i]
        obv_values.append(obv)
    
    obv_trend = obv_values[-1] - obv_values[-10] if len(obv_values) >= 10 else 0
    
    print(f"OBV 추세 (10일): {obv_trend:+,.0f}")
    if obv_trend > 0:
        print("  → 매수 압력 증가")
    elif obv_trend < 0:
        print("  → 매도 압력 증가")
    else:
        print("  → 중립적 흐름")

def generate_trading_signals_demo(data):
    """매매 신호 생성 데모"""
    print("\n⚡ 종합 매매 신호")
    
    # 기술적 지표 재계산
    delta = data['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    bb_middle = data['Close'].rolling(20).mean()
    bb_std = data['Close'].rolling(20).std()
    bb_upper = bb_middle + (bb_std * 2)
    bb_lower = bb_middle - (bb_std * 2)
    
    current_price = data['Close'].iloc[-1]
    current_rsi = rsi.iloc[-1]
    current_bb_upper = bb_upper.iloc[-1]
    current_bb_lower = bb_lower.iloc[-1]
    current_volume = data['Volume'].iloc[-1]
    avg_volume = data['Volume'].rolling(20).mean().iloc[-1]
    
    signal_strength = 0
    signals = []
    
    # RSI 신호
    if current_rsi < 30:
        signal_strength += 1
        signals.append("RSI 과매도 → 매수 신호")
    elif current_rsi > 70:
        signal_strength -= 1
        signals.append("RSI 과매수 → 매도 신호")
    
    # 볼린저 밴드 신호
    if current_price < current_bb_lower:
        signal_strength += 1
        signals.append("볼린저 밴드 하단 터치 → 매수 신호")
    elif current_price > current_bb_upper:
        signal_strength -= 1
        signals.append("볼린저 밴드 상단 돌파 → 매도 신호")
    
    # 거래량 신호
    if current_volume > avg_volume * 1.5:
        signal_strength += 0.5
        signals.append("높은 거래량 → 관심 증가")
    
    # 추세 신호
    sma_20 = data['Close'].rolling(20).mean().iloc[-1]
    if current_price > sma_20:
        signal_strength += 0.5
        signals.append("20일 이평선 상향 → 상승 추세")
    else:
        signal_strength -= 0.5
        signals.append("20일 이평선 하향 → 하락 추세")
    
    # 종합 판단
    if signal_strength >= 2:
        overall_signal = "🟢 강한 매수 신호"
    elif signal_strength >= 1:
        overall_signal = "🔵 약한 매수 신호"
    elif signal_strength <= -2:
        overall_signal = "🔴 강한 매도 신호"
    elif signal_strength <= -1:
        overall_signal = "🟡 약한 매도 신호"
    else:
        overall_signal = "⚪ 중립 - 관망 권장"
    
    print(f"종합 신호: {overall_signal}")
    print(f"신호 강도: {signal_strength:.1f}")
    
    print("\n신호 근거:")
    for signal in signals:
        print(f"  • {signal}")

def main():
    """메인 데모 함수"""
    print("🚀 거래량 및 매물대 분석 시스템 - 데모 버전")
    print("=" * 60)
    print("📊 샘플 데이터로 시스템 기능을 시연합니다")
    print("(실제 QQQ ETF와 유사한 패턴의 가상 데이터)")
    print("=" * 60)
    
    # 샘플 데이터 생성
    print("\n📈 샘플 데이터 생성 중...")
    data = create_sample_data()
    print(f"✅ {len(data)}일간의 주가 데이터 생성 완료")
    
    # 기본 정보
    current_price = data['Close'].iloc[-1]
    start_price = data['Close'].iloc[0]
    total_return = ((current_price - start_price) / start_price * 100)
    
    print(f"\n💰 기본 정보:")
    print(f"분석 기간: {data.index[0].strftime('%Y-%m-%d')} ~ {data.index[-1].strftime('%Y-%m-%d')}")
    print(f"시작가: ${start_price:.2f}")
    print(f"현재가: ${current_price:.2f}")
    print(f"수익률: {total_return:+.2f}%")
    print(f"현재 거래량: {data['Volume'].iloc[-1]:,.0f}")
    
    # 각종 분석 실행
    print(f"\n{'='*60}")
    volume_profile = analyze_volume_profile_demo(data)
    
    find_support_resistance_demo(data)
    
    analyze_technical_indicators_demo(data)
    
    analyze_money_flow_demo(data)
    
    generate_trading_signals_demo(data)
    
    print(f"\n{'='*60}")
    print("📋 분석 완료!")
    print("실제 사용 시에는 yfinance API를 통해 실시간 데이터를 받아옵니다.")
    print("API 제한이 해제되면 advanced_trading_analyzer.py를 실행해보세요.")

if __name__ == "__main__":
    main()
