# 🚀 고급 거래량 및 매물대 분석 시스템

## 📋 개요

이 시스템은 LSTM 기반 주가 예측을 넘어서 **실제 매매에 필요한 핵심 요소들**을 종합적으로 분석합니다:

- **거래량 프로파일 분석** (매물대 탐지)
- **다중 시간대 캔들 패턴 분석** (1시간, 2시간, 4시간봉)
- **실시간 자금 흐름 분석**
- **지지/저항선 자동 탐지**
- **매수/매도 타이밍 신호**

## 🎯 주요 기능

### 1. 종목별 종합 분석
```python
# 메인 분석 시스템 실행
python advanced_trading_analyzer.py
```

**지원 시장:**
- 🇺🇸 **미국 주식**: AAPL, TSLA, NVDA, QQQ, SPY, MSFT 등
- 🇰🇷 **한국 주식**: 005930.KS (삼성전자), 000660.KS (SK하이닉스) 등  
- 🪙 **암호화폐**: BTC-USD, ETH-USD, ADA-USD, SOL-USD 등

### 2. 실시간 거래량 흐름 분석
```python
# 거래량 흐름 전문 분석
python volume_flow_analyzer.py
```

## 📊 분석 결과 예시

### 실행 화면
```
🚀 고급 거래량 및 매물대 분석 시스템
==================================================
지원 시장:
  - 미국 주식: AAPL, TSLA, NVDA, QQQ, SPY 등
  - 한국 주식: 005930.KS (삼성전자), 000660.KS (SK하이닉스) 등
  - 암호화폐: BTC-USD, ETH-USD, ADA-USD 등
==================================================

분석할 종목 코드를 입력하세요 (종료: 'quit'): AAPL

📊 AAPL 데이터 수집 중...
✅ 1h 데이터: 720개 캔들
✅ 2h 데이터: 360개 캔들  
✅ 4h 데이터: 180개 캔들
✅ 1d 데이터: 90개 캔들
```

### 분석 보고서 예시
```
============================================================
📊 AAPL 종합 분석 보고서
시장: US | 분석 시간: 2025-01-09 15:30:25
============================================================

🎯 매물대 분석 (거래량 프로파일)
현재가: $185.42
POC (최대 거래량 가격): $183.75
POC 대비 현재가: +0.91%

주요 매물대 (상위 5개):
  $183.75 (현재가 대비 -0.90%) - 거래량: 2,450,000
  $186.20 (현재가 대비 +0.42%) - 거래량: 2,180,000
  $181.90 (현재가 대비 -1.90%) - 거래량: 1,950,000
  $188.50 (현재가 대비 +1.66%) - 거래량: 1,820,000
  $179.30 (현재가 대비 -3.30%) - 거래량: 1,650,000

📈 지지/저항선 분석
주요 저항선:
  $188.50 (+1.66%) - 강도: 8.5
  $191.20 (+3.12%) - 강도: 7.2
  $194.80 (+5.06%) - 강도: 6.8

주요 지지선:
  $181.90 (-1.90%) - 강도: 9.1
  $179.30 (-3.30%) - 강도: 8.7
  $176.50 (-4.81%) - 강도: 7.9

💰 자금 흐름 분석
VWAP: $184.15
현재가 vs VWAP: +0.69%
MFI (자금흐름지수): 65.3
  → 중립 상태

⚡ 다중 시간대 매매 신호
1h: 🔵 약한 매수 (강도: 0.5)
    - 높은 거래량
4h: 🟢 강한 매수 (강도: 1.5)
    - RSI 과매도 (28.5)
    - 볼린저 밴드 하단 터치
1d: ⚪ 중립 (강도: 0.0)

🎯 종합 매매 신호
🔵 약한 매수 신호
신호 강도: 2.0

🕯️ 최근 캔들 패턴
  01-09 13:30: HAMMER (중요도: 7.2)
  01-09 14:15: BULLISH_ENGULFING (중요도: 8.5)
  01-09 15:00: DOJI (중요도: 3.1)
```

## 🔧 설치 및 실행

### 1. 필요한 라이브러리 설치
```bash
pip install yfinance pandas numpy matplotlib seaborn scikit-learn tensorflow
```

### 2. 실행 방법

#### 종합 분석 (추천)
```bash
python advanced_trading_analyzer.py
```

#### 거래량 전문 분석
```bash
python volume_flow_analyzer.py
```

#### 기존 LSTM 모델 개선
```bash
python trading_strategy_analysis.py
```

## 📈 핵심 분석 지표

### 1. 거래량 프로파일 (Volume Profile)
- **POC (Point of Control)**: 최대 거래량이 발생한 가격대
- **매물대**: 거래량이 집중된 가격 구간
- **현재가 vs POC**: 현재 가격의 상대적 위치

### 2. 지지/저항선 (Support/Resistance)
- **자동 탐지**: 과거 고점/저점 기반 자동 계산
- **강도 측정**: 터치 횟수와 거래량을 고려한 신뢰도
- **거리 계산**: 현재가 대비 상대적 거리

### 3. 자금 흐름 지표
- **VWAP**: 거래량 가중 평균 가격
- **MFI**: 자금 흐름 지수 (0-100)
- **OBV**: 온 밸런스 볼륨

### 4. 캔들 패턴 분석
- **해머/행잉맨**: 반전 신호
- **엔걸핑**: 추세 전환 신호  
- **도지**: 우유부단한 시장 심리
- **유성**: 상승 저항 신호

## 💡 실전 활용 팁

### 1. 매수 타이밍
```
✅ 강한 매수 신호 조건:
- POC 근처에서 지지 확인
- 높은 거래량과 함께 상승
- 다중 시간대 신호 일치
- RSI 과매도 구간에서 반등
```

### 2. 매도 타이밍  
```
✅ 매도 고려 조건:
- 주요 저항선 근처 도달
- 거래량 감소와 함께 상승 둔화
- MFI 과매수 구간 진입
- 베어리시 캔들 패턴 출현
```

### 3. 리스크 관리
```
⚠️ 주의사항:
- 단일 지표에만 의존하지 말 것
- 거래량 없는 움직임은 신뢰도 낮음
- 시장 전체 상황도 함께 고려
- 손절매 라인 미리 설정
```

## 🔄 지속적 개선 방안

### 1. 데이터 품질 향상
- 실시간 데이터 API 연동
- 더 세밀한 시간대 분석 (1분봉, 5분봉)
- 시장 외 시간 데이터 포함

### 2. 분석 기능 확장
- 섹터별 상대 강도 분석
- 기관/개인 거래량 분리 분석
- 뉴스/이벤트 영향도 분석

### 3. 자동화 강화
- 알림 시스템 구축
- 자동 매매 신호 생성
- 포트폴리오 최적화 연동

## 📞 문의 및 지원

이 시스템은 교육 및 연구 목적으로 제작되었습니다. 
실제 투자 결정은 본인의 판단과 책임 하에 이루어져야 합니다.

**면책 조항**: 이 분석 도구는 투자 조언이 아니며, 투자 손실에 대한 책임을 지지 않습니다.
