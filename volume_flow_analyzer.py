"""
실시간 거래량 흐름 및 매수/매도 타이밍 분석
Real-time Volume Flow and Buy/Sell Timing Analysis
"""

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class VolumeFlowAnalyzer:
    def __init__(self):
        self.data = None
        self.symbol = None
    
    def analyze_intraday_volume_flow(self, symbol, period="5d"):
        """일중 거래량 흐름 분석"""
        self.symbol = symbol
        
        try:
            ticker = yf.Ticker(symbol)
            # 5분봉 데이터로 세밀한 분석
            data = ticker.history(period=period, interval="5m")
            
            if data.empty:
                print(f"❌ {symbol} 데이터를 가져올 수 없습니다.")
                return None
            
            self.data = data
            
            # 거래량 흐름 지표 계산
            data['price_change'] = data['Close'].diff()
            data['volume_change'] = data['Volume'].diff()
            
            # 매수/매도 압력 추정
            data['buy_pressure'] = np.where(data['price_change'] > 0, data['Volume'], 0)
            data['sell_pressure'] = np.where(data['price_change'] < 0, data['Volume'], 0)
            
            # 누적 매수/매도 압력
            data['cumulative_buy'] = data['buy_pressure'].cumsum()
            data['cumulative_sell'] = data['sell_pressure'].cumsum()
            
            # 매수/매도 비율
            data['buy_sell_ratio'] = data['cumulative_buy'] / (data['cumulative_sell'] + 1)
            
            # 거래량 가중 평균 가격 (VWAP)
            data['typical_price'] = (data['High'] + data['Low'] + data['Close']) / 3
            data['volume_price'] = data['typical_price'] * data['Volume']
            data['cumulative_volume'] = data['Volume'].cumsum()
            data['cumulative_volume_price'] = data['volume_price'].cumsum()
            data['vwap'] = data['cumulative_volume_price'] / data['cumulative_volume']
            
            # 거래량 스파이크 감지
            volume_mean = data['Volume'].rolling(20).mean()
            volume_std = data['Volume'].rolling(20).std()
            data['volume_spike'] = (data['Volume'] - volume_mean) / volume_std
            
            return data
            
        except Exception as e:
            print(f"❌ 데이터 분석 실패: {e}")
            return None
    
    def detect_accumulation_distribution(self, data=None):
        """누적/분산 패턴 감지"""
        if data is None:
            data = self.data
        
        if data is None or data.empty:
            return None
        
        # A/D Line 계산
        money_flow_multiplier = ((data['Close'] - data['Low']) - (data['High'] - data['Close'])) / (data['High'] - data['Low'])
        money_flow_multiplier = money_flow_multiplier.fillna(0)
        money_flow_volume = money_flow_multiplier * data['Volume']
        ad_line = money_flow_volume.cumsum()
        
        # 추세 분석
        ad_slope = ad_line.diff(20)  # 20기간 기울기
        
        patterns = []
        for i in range(20, len(data)):
            current_slope = ad_slope.iloc[i]
            price_change = (data['Close'].iloc[i] - data['Close'].iloc[i-20]) / data['Close'].iloc[i-20]
            
            # 패턴 분류
            if current_slope > 0 and price_change > 0:
                pattern = "ACCUMULATION"  # 누적 (가격 상승 + 거래량 증가)
            elif current_slope > 0 and price_change < 0:
                pattern = "BUYING_PRESSURE"  # 매수 압력 (가격 하락하지만 거래량 증가)
            elif current_slope < 0 and price_change > 0:
                pattern = "SELLING_PRESSURE"  # 매도 압력 (가격 상승하지만 거래량 감소)
            elif current_slope < 0 and price_change < 0:
                pattern = "DISTRIBUTION"  # 분산 (가격 하락 + 거래량 감소)
            else:
                pattern = "NEUTRAL"
            
            patterns.append({
                'timestamp': data.index[i],
                'pattern': pattern,
                'ad_line': ad_line.iloc[i],
                'slope': current_slope,
                'price': data['Close'].iloc[i],
                'volume': data['Volume'].iloc[i]
            })
        
        return pd.DataFrame(patterns)
    
    def find_volume_breakouts(self, data=None, volume_threshold=2.0):
        """거래량 급증 구간 탐지"""
        if data is None:
            data = self.data
        
        if data is None or data.empty:
            return None
        
        # 평균 거래량 대비 배수 계산
        volume_avg = data['Volume'].rolling(20).mean()
        volume_ratio = data['Volume'] / volume_avg
        
        # 거래량 급증 구간 찾기
        breakouts = []
        
        for i in range(20, len(data)):
            if volume_ratio.iloc[i] > volume_threshold:
                price_change = (data['Close'].iloc[i] - data['Open'].iloc[i]) / data['Open'].iloc[i]
                
                # 방향성 판단
                if price_change > 0.01:  # 1% 이상 상승
                    direction = "BULLISH_BREAKOUT"
                elif price_change < -0.01:  # 1% 이상 하락
                    direction = "BEARISH_BREAKOUT"
                else:
                    direction = "NEUTRAL_BREAKOUT"
                
                breakouts.append({
                    'timestamp': data.index[i],
                    'direction': direction,
                    'volume_ratio': volume_ratio.iloc[i],
                    'price_change': price_change * 100,
                    'price': data['Close'].iloc[i],
                    'volume': data['Volume'].iloc[i]
                })
        
        return pd.DataFrame(breakouts)
    
    def calculate_smart_money_index(self, data=None):
        """스마트 머니 지수 계산 (기관 투자자 움직임 추정)"""
        if data is None:
            data = self.data
        
        if data is None or data.empty:
            return None
        
        # 시간대별 가중치 (장 시작/마감 시간에 기관 거래 집중)
        data['hour'] = data.index.hour
        
        # 스마트 머니 가중치 (9:30-10:30, 15:30-16:00에 높은 가중치)
        smart_money_weight = []
        for hour in data['hour']:
            if 9 <= hour <= 10 or 15 <= hour <= 16:
                weight = 2.0  # 기관 거래 시간대
            elif 11 <= hour <= 14:
                weight = 0.5  # 개인 투자자 거래 시간대
            else:
                weight = 1.0  # 일반 시간대
            smart_money_weight.append(weight)
        
        data['smart_weight'] = smart_money_weight
        
        # 가격 변화와 거래량을 고려한 스마트 머니 지수
        data['price_volume_trend'] = data['Close'].diff() * data['Volume'] * data['smart_weight']
        data['smart_money_index'] = data['price_volume_trend'].rolling(20).sum()
        
        return data[['Close', 'Volume', 'smart_money_index', 'smart_weight']]
    
    def generate_entry_exit_signals(self, data=None):
        """진입/청산 신호 생성"""
        if data is None:
            data = self.data
        
        if data is None or data.empty:
            return None
        
        signals = []
        
        # 필요한 지표들 계산
        volume_avg = data['Volume'].rolling(20).mean()
        price_sma = data['Close'].rolling(20).mean()
        
        for i in range(20, len(data)):
            current = data.iloc[i]
            prev = data.iloc[i-1]
            
            signal_strength = 0
            reasons = []
            
            # 1. 거래량 조건
            volume_ratio = current['Volume'] / volume_avg.iloc[i]
            if volume_ratio > 1.5:
                signal_strength += 1
                reasons.append(f"높은 거래량 ({volume_ratio:.1f}배)")
            
            # 2. 가격 조건
            if current['Close'] > price_sma.iloc[i]:
                signal_strength += 1
                reasons.append("이평선 상향")
            else:
                signal_strength -= 1
                reasons.append("이평선 하향")
            
            # 3. VWAP 조건
            if 'vwap' in current and current['Close'] > current['vwap']:
                signal_strength += 0.5
                reasons.append("VWAP 상향")
            
            # 4. 매수/매도 압력 조건
            if 'buy_sell_ratio' in current and current['buy_sell_ratio'] > 1.2:
                signal_strength += 1
                reasons.append("매수 압력 우세")
            elif 'buy_sell_ratio' in current and current['buy_sell_ratio'] < 0.8:
                signal_strength -= 1
                reasons.append("매도 압력 우세")
            
            # 신호 분류
            if signal_strength >= 2:
                signal = "STRONG_BUY"
            elif signal_strength >= 1:
                signal = "BUY"
            elif signal_strength <= -2:
                signal = "STRONG_SELL"
            elif signal_strength <= -1:
                signal = "SELL"
            else:
                signal = "HOLD"
            
            signals.append({
                'timestamp': current.name,
                'signal': signal,
                'strength': signal_strength,
                'price': current['Close'],
                'volume': current['Volume'],
                'reasons': reasons
            })
        
        return pd.DataFrame(signals)
    
    def print_volume_analysis_report(self):
        """거래량 분석 보고서 출력"""
        if self.data is None or self.data.empty:
            print("분석할 데이터가 없습니다.")
            return
        
        print(f"\n{'='*60}")
        print(f"📊 {self.symbol} 거래량 흐름 분석 보고서")
        print(f"분석 기간: {self.data.index[0].strftime('%Y-%m-%d %H:%M')} ~ {self.data.index[-1].strftime('%Y-%m-%d %H:%M')}")
        print(f"{'='*60}")
        
        # 1. 기본 통계
        current_price = self.data['Close'].iloc[-1]
        current_volume = self.data['Volume'].iloc[-1]
        avg_volume = self.data['Volume'].mean()
        
        print(f"\n📈 기본 정보")
        print(f"현재가: ${current_price:.2f}")
        print(f"현재 거래량: {current_volume:,.0f}")
        print(f"평균 거래량: {avg_volume:,.0f}")
        print(f"거래량 비율: {current_volume/avg_volume:.2f}배")
        
        # 2. 누적/분산 패턴
        print(f"\n🔄 누적/분산 패턴")
        ad_patterns = self.detect_accumulation_distribution()
        if ad_patterns is not None and not ad_patterns.empty:
            recent_pattern = ad_patterns.iloc[-1]
            print(f"현재 패턴: {recent_pattern['pattern']}")
            
            pattern_counts = ad_patterns['pattern'].value_counts()
            print("최근 패턴 분포:")
            for pattern, count in pattern_counts.head(3).items():
                print(f"  {pattern}: {count}회")
        
        # 3. 거래량 급증 구간
        print(f"\n💥 거래량 급증 분석")
        breakouts = self.find_volume_breakouts()
        if breakouts is not None and not breakouts.empty:
            recent_breakouts = breakouts.tail(5)
            print("최근 거래량 급증 구간:")
            for i, breakout in recent_breakouts.iterrows():
                print(f"  {breakout['timestamp'].strftime('%m-%d %H:%M')}: {breakout['direction']} "
                      f"(거래량 {breakout['volume_ratio']:.1f}배, 가격변화 {breakout['price_change']:+.2f}%)")
        else:
            print("최근 특별한 거래량 급증이 감지되지 않았습니다.")
        
        # 4. 스마트 머니 지수
        print(f"\n🧠 스마트 머니 분석")
        smart_money = self.calculate_smart_money_index()
        if smart_money is not None and not smart_money.empty:
            current_smi = smart_money['smart_money_index'].iloc[-1]
            smi_trend = smart_money['smart_money_index'].diff(10).iloc[-1]
            
            print(f"스마트 머니 지수: {current_smi:,.0f}")
            if smi_trend > 0:
                print("  → 기관 매수 흐름 감지")
            elif smi_trend < 0:
                print("  → 기관 매도 흐름 감지")
            else:
                print("  → 중립적 흐름")
        
        # 5. 매매 신호
        print(f"\n⚡ 매매 신호")
        signals = self.generate_entry_exit_signals()
        if signals is not None and not signals.empty:
            current_signal = signals.iloc[-1]
            print(f"현재 신호: {current_signal['signal']} (강도: {current_signal['strength']:.1f})")
            print("신호 근거:")
            for reason in current_signal['reasons']:
                print(f"  - {reason}")
        
        print(f"\n{'='*60}")


def main():
    """메인 실행 함수"""
    analyzer = VolumeFlowAnalyzer()
    
    print("📊 실시간 거래량 흐름 분석 시스템")
    print("=" * 50)
    
    while True:
        symbol = input("\n분석할 종목 코드를 입력하세요 (종료: 'quit'): ").strip().upper()
        
        if symbol.lower() == 'quit':
            break
        
        if not symbol:
            continue
        
        print(f"\n📊 {symbol} 거래량 흐름 분석 중...")
        
        # 일중 거래량 흐름 분석
        data = analyzer.analyze_intraday_volume_flow(symbol)
        
        if data is not None:
            analyzer.print_volume_analysis_report()
        else:
            print("❌ 분석에 실패했습니다.")


if __name__ == "__main__":
    main()
