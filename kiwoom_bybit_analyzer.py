"""
키움증권 + 바이비트 API 연동 거래량 및 매물대 분석 시스템
Kiwoom Securities + Bybit API Integration for Volume and Order Book Analysis
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import hmac
import hashlib
import json
import warnings
warnings.filterwarnings('ignore')

class BybitAnalyzer:
    """바이비트 API 분석기"""
    
    def __init__(self, api_key=None, api_secret=None):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.bybit.com"
        
    def get_kline_data(self, symbol, interval, limit=200):
        """바이비트 캔들 데이터 수집"""
        endpoint = "/v5/market/kline"
        url = self.base_url + endpoint
        
        params = {
            'category': 'spot',  # spot, linear, inverse
            'symbol': symbol,
            'interval': interval,  # 1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M
            'limit': limit
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['retCode'] != 0:
                print(f"❌ 바이비트 API 오류: {data['retMsg']}")
                return None
            
            # 데이터 파싱
            klines = data['result']['list']
            
            df_data = []
            for kline in klines:
                df_data.append({
                    'timestamp': pd.to_datetime(int(kline[0]), unit='ms'),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5]),
                    'turnover': float(kline[6])
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # 컬럼명 표준화
            df.columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Turnover']
            
            return df
            
        except Exception as e:
            print(f"❌ 바이비트 데이터 수집 실패: {e}")
            return None
    
    def get_orderbook(self, symbol, limit=50):
        """바이비트 호가창 데이터 수집"""
        endpoint = "/v5/market/orderbook"
        url = self.base_url + endpoint
        
        params = {
            'category': 'spot',
            'symbol': symbol,
            'limit': limit
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['retCode'] != 0:
                print(f"❌ 바이비트 호가창 오류: {data['retMsg']}")
                return None
            
            result = data['result']
            
            # 매수/매도 호가 파싱
            bids = [[float(bid[0]), float(bid[1])] for bid in result['b']]
            asks = [[float(ask[0]), float(ask[1])] for ask in result['a']]
            
            return {
                'bids': bids,  # [가격, 수량]
                'asks': asks,
                'timestamp': pd.to_datetime(int(result['ts']), unit='ms')
            }
            
        except Exception as e:
            print(f"❌ 바이비트 호가창 수집 실패: {e}")
            return None
    
    def get_recent_trades(self, symbol, limit=100):
        """바이비트 최근 거래 내역"""
        endpoint = "/v5/market/recent-trade"
        url = self.base_url + endpoint
        
        params = {
            'category': 'spot',
            'symbol': symbol,
            'limit': limit
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['retCode'] != 0:
                print(f"❌ 바이비트 거래내역 오류: {data['retMsg']}")
                return None
            
            trades = data['result']['list']
            
            df_data = []
            for trade in trades:
                df_data.append({
                    'timestamp': pd.to_datetime(int(trade['time']), unit='ms'),
                    'price': float(trade['price']),
                    'size': float(trade['size']),
                    'side': trade['side'],  # Buy/Sell
                    'is_block_trade': trade['isBlockTrade']
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            return df
            
        except Exception as e:
            print(f"❌ 바이비트 거래내역 수집 실패: {e}")
            return None
    
    def analyze_crypto_volume_flow(self, symbol):
        """암호화폐 거래량 흐름 분석"""
        print(f"\n📊 {symbol} 바이비트 거래량 흐름 분석")
        print("=" * 50)
        
        # 1. 다중 시간대 데이터 수집
        timeframes = {
            '1분': self.get_kline_data(symbol, '1', 100),
            '5분': self.get_kline_data(symbol, '5', 100),
            '1시간': self.get_kline_data(symbol, '60', 100),
            '4시간': self.get_kline_data(symbol, '240', 100),
            '일봉': self.get_kline_data(symbol, 'D', 100)
        }
        
        # 2. 호가창 분석
        orderbook = self.get_orderbook(symbol)
        
        # 3. 최근 거래 분석
        recent_trades = self.get_recent_trades(symbol)
        
        # 분석 결과 출력
        self.print_crypto_analysis(symbol, timeframes, orderbook, recent_trades)
    
    def print_crypto_analysis(self, symbol, timeframes, orderbook, recent_trades):
        """암호화폐 분석 결과 출력"""
        
        # 기본 정보
        daily_data = timeframes['일봉']
        if daily_data is not None and not daily_data.empty:
            current_price = daily_data['Close'].iloc[-1]
            prev_price = daily_data['Close'].iloc[-2]
            price_change = current_price - prev_price
            price_change_pct = (price_change / prev_price) * 100
            
            print(f"\n💰 기본 정보:")
            print(f"현재가: ${current_price:,.2f}")
            print(f"24시간 변동: {price_change:+,.2f} ({price_change_pct:+.2f}%)")
            print(f"24시간 거래량: {daily_data['Volume'].iloc[-1]:,.2f}")
            print(f"24시간 거래대금: ${daily_data['Turnover'].iloc[-1]:,.2f}")
        
        # 호가창 분석
        if orderbook:
            print(f"\n📋 호가창 분석:")
            
            # 매수/매도 벽 분석
            bids = orderbook['bids'][:10]  # 상위 10개 매수 호가
            asks = orderbook['asks'][:10]  # 상위 10개 매도 호가
            
            total_bid_volume = sum([bid[1] for bid in bids])
            total_ask_volume = sum([ask[1] for ask in asks])
            
            print(f"매수 벽 (상위 10단계): {total_bid_volume:,.2f}")
            print(f"매도 벽 (상위 10단계): {total_ask_volume:,.2f}")
            print(f"매수/매도 비율: {total_bid_volume/total_ask_volume:.2f}")
            
            # 스프레드 분석
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid
            spread_pct = (spread / best_bid) * 100
            
            print(f"최우선 매수호가: ${best_bid:,.2f}")
            print(f"최우선 매도호가: ${best_ask:,.2f}")
            print(f"스프레드: ${spread:,.2f} ({spread_pct:.3f}%)")
        
        # 최근 거래 분석
        if recent_trades is not None and not recent_trades.empty:
            print(f"\n💹 최근 거래 분석:")
            
            # 매수/매도 압력
            buy_trades = recent_trades[recent_trades['side'] == 'Buy']
            sell_trades = recent_trades[recent_trades['side'] == 'Sell']
            
            buy_volume = buy_trades['size'].sum()
            sell_volume = sell_trades['size'].sum()
            
            print(f"최근 100건 중 매수 거래량: {buy_volume:,.2f}")
            print(f"최근 100건 중 매도 거래량: {sell_volume:,.2f}")
            print(f"매수/매도 거래량 비율: {buy_volume/sell_volume:.2f}")
            
            # 대량 거래 분석
            large_trades = recent_trades[recent_trades['size'] > recent_trades['size'].quantile(0.9)]
            if not large_trades.empty:
                print(f"대량 거래 (상위 10%): {len(large_trades)}건")
                avg_large_trade = large_trades['size'].mean()
                print(f"대량 거래 평균 크기: {avg_large_trade:,.2f}")
        
        # 다중 시간대 신호
        print(f"\n⚡ 다중 시간대 신호:")
        for tf_name, tf_data in timeframes.items():
            if tf_data is not None and not tf_data.empty and len(tf_data) >= 20:
                current = tf_data['Close'].iloc[-1]
                sma_20 = tf_data['Close'].rolling(20).mean().iloc[-1]
                
                if current > sma_20:
                    signal = "🟢 상승"
                else:
                    signal = "🔴 하락"
                
                volume_avg = tf_data['Volume'].rolling(10).mean().iloc[-1]
                volume_current = tf_data['Volume'].iloc[-1]
                volume_ratio = volume_current / volume_avg
                
                print(f"  {tf_name}: {signal} (거래량 {volume_ratio:.1f}배)")


class KiwoomAnalyzer:
    """키움증권 API 분석기 (OpenAPI+ 기반)"""
    
    def __init__(self):
        self.app_key = None
        self.app_secret = None
        self.access_token = None
        self.base_url = "https://openapi.kiwoom.com"
        
    def setup_kiwoom_api(self):
        """키움증권 API 설정"""
        print("\n🔑 키움증권 OpenAPI+ 설정")
        print("=" * 50)
        print("1. 키움증권 홈페이지에서 OpenAPI+ 신청")
        print("2. 앱 키(App Key)와 시크릿 키(Secret Key) 발급")
        print("3. 모의투자 또는 실계좌 선택")
        print("=" * 50)
        
        self.app_key = input("App Key를 입력하세요: ").strip()
        self.app_secret = input("Secret Key를 입력하세요: ").strip()
        
        if not self.app_key or not self.app_secret:
            print("❌ API 키가 올바르게 입력되지 않았습니다.")
            return False
        
        return True
    
    def get_access_token(self):
        """액세스 토큰 발급"""
        # 실제 구현에서는 OAuth 인증 과정 필요
        print("🔐 액세스 토큰 발급 중...")
        # 여기서는 시뮬레이션
        self.access_token = "sample_access_token"
        return True
    
    def get_stock_price(self, stock_code):
        """한국 주식 현재가 조회"""
        # 실제 API 호출 시뮬레이션
        print(f"📊 {stock_code} 현재가 조회 중...")
        
        # 샘플 데이터 (실제로는 API 호출)
        sample_data = {
            '005930': {'name': '삼성전자', 'price': 71000, 'change': 1000, 'volume': 15000000},
            '000660': {'name': 'SK하이닉스', 'price': 89000, 'change': -2000, 'volume': 8000000},
            '035420': {'name': 'NAVER', 'price': 185000, 'change': 3000, 'volume': 500000},
            '051910': {'name': 'LG화학', 'price': 420000, 'change': -5000, 'volume': 300000},
            '006400': {'name': '삼성SDI', 'price': 380000, 'change': 8000, 'volume': 400000}
        }
        
        if stock_code in sample_data:
            return sample_data[stock_code]
        else:
            return None
    
    def get_stock_chart(self, stock_code, period='D', count=100):
        """한국 주식 차트 데이터 조회"""
        print(f"📈 {stock_code} 차트 데이터 조회 중...")
        
        # 샘플 차트 데이터 생성
        np.random.seed(42)
        dates = pd.date_range(end=datetime.now(), periods=count, freq='D')
        dates = dates[dates.weekday < 5]  # 주말 제외
        
        base_price = 70000 if stock_code == '005930' else 85000
        
        data = []
        for i, date in enumerate(dates):
            price = base_price + np.random.normal(0, 2000) + i * 100
            volume = np.random.randint(5000000, 20000000)
            
            data.append({
                'date': date,
                'open': price + np.random.normal(0, 500),
                'high': price + abs(np.random.normal(0, 1000)),
                'low': price - abs(np.random.normal(0, 1000)),
                'close': price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('date', inplace=True)
        
        return df
    
    def analyze_korean_stock(self, stock_code):
        """한국 주식 분석"""
        print(f"\n📊 {stock_code} 키움증권 분석")
        print("=" * 50)
        
        # 현재가 정보
        current_info = self.get_stock_price(stock_code)
        if current_info is None:
            print(f"❌ {stock_code} 종목을 찾을 수 없습니다.")
            return
        
        # 차트 데이터
        chart_data = self.get_stock_chart(stock_code)
        
        # 분석 결과 출력
        self.print_korean_stock_analysis(stock_code, current_info, chart_data)
    
    def print_korean_stock_analysis(self, stock_code, current_info, chart_data):
        """한국 주식 분석 결과 출력"""
        
        print(f"\n💰 기본 정보:")
        print(f"종목명: {current_info['name']}")
        print(f"현재가: {current_info['price']:,}원")
        print(f"전일대비: {current_info['change']:+,}원 ({current_info['change']/current_info['price']*100:+.2f}%)")
        print(f"거래량: {current_info['volume']:,}주")
        
        # 기술적 분석
        if chart_data is not None and not chart_data.empty:
            current_price = chart_data['close'].iloc[-1]
            
            # 이동평균
            ma5 = chart_data['close'].rolling(5).mean().iloc[-1]
            ma20 = chart_data['close'].rolling(20).mean().iloc[-1]
            ma60 = chart_data['close'].rolling(60).mean().iloc[-1]
            
            print(f"\n📈 기술적 분석:")
            print(f"5일 이평선: {ma5:,.0f}원 ({(current_price-ma5)/ma5*100:+.2f}%)")
            print(f"20일 이평선: {ma20:,.0f}원 ({(current_price-ma20)/ma20*100:+.2f}%)")
            print(f"60일 이평선: {ma60:,.0f}원 ({(current_price-ma60)/ma60*100:+.2f}%)")
            
            # 거래량 분석
            avg_volume = chart_data['volume'].rolling(20).mean().iloc[-1]
            volume_ratio = current_info['volume'] / avg_volume
            
            print(f"\n📊 거래량 분석:")
            print(f"20일 평균 거래량: {avg_volume:,.0f}주")
            print(f"거래량 비율: {volume_ratio:.2f}배")
            
            if volume_ratio > 2:
                print("  → 급증한 거래량 (관심 집중)")
            elif volume_ratio > 1.5:
                print("  → 높은 거래량 (관심 증가)")
            else:
                print("  → 평범한 거래량")
            
            # 지지/저항선
            recent_high = chart_data['high'].rolling(20).max().iloc[-1]
            recent_low = chart_data['low'].rolling(20).min().iloc[-1]
            
            print(f"\n📊 지지/저항선:")
            print(f"20일 최고가: {recent_high:,.0f}원 ({(recent_high-current_price)/current_price*100:+.2f}%)")
            print(f"20일 최저가: {recent_low:,.0f}원 ({(current_price-recent_low)/recent_low*100:+.2f}%)")
            
            # 매매 신호
            print(f"\n⚡ 매매 신호:")
            signals = []
            
            if current_price > ma5 > ma20:
                signals.append("단기 상승 추세 (5일선 > 20일선)")
            elif current_price < ma5 < ma20:
                signals.append("단기 하락 추세 (5일선 < 20일선)")
            
            if volume_ratio > 1.5:
                signals.append("높은 거래량 → 관심 증가")
            
            if current_price >= recent_high * 0.98:
                signals.append("고점 근접 → 저항 확인 필요")
            elif current_price <= recent_low * 1.02:
                signals.append("저점 근접 → 지지 확인 필요")
            
            for signal in signals:
                print(f"  • {signal}")


def main():
    """메인 실행 함수"""
    print("🚀 키움증권 + 바이비트 통합 분석 시스템")
    print("=" * 60)
    print("지원 시장:")
    print("  📈 한국 주식 (키움증권): 005930, 000660, 035420 등")
    print("  🪙 암호화폐 (바이비트): BTCUSDT, ETHUSDT, ADAUSDT 등")
    print("=" * 60)
    
    # 분석기 초기화
    kiwoom = KiwoomAnalyzer()
    bybit = BybitAnalyzer()
    
    while True:
        print("\n📊 분석 옵션을 선택하세요:")
        print("1. 한국 주식 분석 (키움증권)")
        print("2. 암호화폐 분석 (바이비트)")
        print("3. 종료")
        
        choice = input("선택 (1-3): ").strip()
        
        if choice == '1':
            # 키움증권 API 설정 (최초 1회)
            if not kiwoom.app_key:
                if not kiwoom.setup_kiwoom_api():
                    continue
                kiwoom.get_access_token()
            
            stock_code = input("한국 주식 코드 입력 (예: 005930): ").strip()
            if stock_code:
                kiwoom.analyze_korean_stock(stock_code)
        
        elif choice == '2':
            symbol = input("암호화폐 심볼 입력 (예: BTCUSDT): ").strip().upper()
            if symbol:
                bybit.analyze_crypto_volume_flow(symbol)
        
        elif choice == '3':
            print("프로그램을 종료합니다.")
            break
        
        else:
            print("올바른 옵션을 선택해주세요.")


if __name__ == "__main__":
    main()
