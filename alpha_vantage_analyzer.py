"""
Alpha Vantage API를 사용한 안정적인 데이터 수집
Alpha Vantage API Key 필요: https://www.alphavantage.co/support/#api-key
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime
import time
import warnings
warnings.filterwarnings('ignore')

class AlphaVantageAnalyzer:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://www.alphavantage.co/query"
        
    def get_intraday_data(self, symbol, interval="60min"):
        """인트라데이 데이터 수집"""
        params = {
            'function': 'TIME_SERIES_INTRADAY',
            'symbol': symbol,
            'interval': interval,
            'apikey': self.api_key,
            'outputsize': 'full'
        }
        
        try:
            response = requests.get(self.base_url, params=params)
            data = response.json()
            
            if 'Error Message' in data:
                print(f"❌ 오류: {data['Error Message']}")
                return None
            
            if 'Note' in data:
                print(f"⚠️ API 제한: {data['Note']}")
                return None
            
            # 데이터 파싱
            time_series_key = f'Time Series ({interval})'
            if time_series_key not in data:
                print(f"❌ 데이터를 찾을 수 없습니다: {list(data.keys())}")
                return None
            
            time_series = data[time_series_key]
            
            # DataFrame 변환
            df_data = []
            for timestamp, values in time_series.items():
                df_data.append({
                    'timestamp': pd.to_datetime(timestamp),
                    'open': float(values['1. open']),
                    'high': float(values['2. high']),
                    'low': float(values['3. low']),
                    'close': float(values['4. close']),
                    'volume': int(values['5. volume'])
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # 컬럼명 표준화
            df.columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            
            return df
            
        except Exception as e:
            print(f"❌ 데이터 수집 실패: {e}")
            return None
    
    def get_daily_data(self, symbol):
        """일봉 데이터 수집"""
        params = {
            'function': 'TIME_SERIES_DAILY',
            'symbol': symbol,
            'apikey': self.api_key,
            'outputsize': 'full'
        }
        
        try:
            response = requests.get(self.base_url, params=params)
            data = response.json()
            
            if 'Error Message' in data:
                print(f"❌ 오류: {data['Error Message']}")
                return None
            
            if 'Note' in data:
                print(f"⚠️ API 제한: {data['Note']}")
                return None
            
            time_series = data['Time Series (Daily)']
            
            # DataFrame 변환
            df_data = []
            for date, values in time_series.items():
                df_data.append({
                    'date': pd.to_datetime(date),
                    'open': float(values['1. open']),
                    'high': float(values['2. high']),
                    'low': float(values['3. low']),
                    'close': float(values['4. close']),
                    'volume': int(values['5. volume'])
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
            
            # 컬럼명 표준화
            df.columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            
            return df
            
        except Exception as e:
            print(f"❌ 데이터 수집 실패: {e}")
            return None
    
    def get_technical_indicators(self, symbol, indicator='RSI', time_period=14):
        """기술적 지표 데이터 수집"""
        params = {
            'function': indicator,
            'symbol': symbol,
            'interval': 'daily',
            'time_period': time_period,
            'series_type': 'close',
            'apikey': self.api_key
        }
        
        try:
            response = requests.get(self.base_url, params=params)
            data = response.json()
            
            if 'Error Message' in data:
                print(f"❌ 오류: {data['Error Message']}")
                return None
            
            # RSI 데이터 파싱
            if indicator == 'RSI':
                technical_data = data[f'Technical Analysis: {indicator}']
                
                df_data = []
                for date, values in technical_data.items():
                    df_data.append({
                        'date': pd.to_datetime(date),
                        'RSI': float(values['RSI'])
                    })
                
                df = pd.DataFrame(df_data)
                df.set_index('date', inplace=True)
                df.sort_index(inplace=True)
                
                return df
            
        except Exception as e:
            print(f"❌ 기술적 지표 수집 실패: {e}")
            return None
    
    def analyze_with_api(self, symbol):
        """API를 사용한 종합 분석"""
        print(f"📊 {symbol} Alpha Vantage API 분석 시작...")
        
        # 1. 일봉 데이터
        print("📈 일봉 데이터 수집 중...")
        daily_data = self.get_daily_data(symbol)
        if daily_data is None:
            return
        
        time.sleep(12)  # API 제한 (5 calls per minute)
        
        # 2. 1시간봉 데이터
        print("⏰ 1시간봉 데이터 수집 중...")
        hourly_data = self.get_intraday_data(symbol, "60min")
        if hourly_data is None:
            print("⚠️ 1시간봉 데이터 수집 실패, 일봉 데이터로 분석 진행")
            hourly_data = daily_data.copy()
        
        time.sleep(12)  # API 제한
        
        # 3. RSI 데이터
        print("📊 RSI 지표 수집 중...")
        rsi_data = self.get_technical_indicators(symbol, 'RSI')
        
        # 분석 실행
        self.perform_analysis(symbol, daily_data, hourly_data, rsi_data)
    
    def perform_analysis(self, symbol, daily_data, hourly_data, rsi_data):
        """수집된 데이터로 분석 수행"""
        print(f"\n{'='*60}")
        print(f"📊 {symbol} Alpha Vantage 분석 보고서")
        print(f"분석 시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        # 기본 정보
        current_price = daily_data['Close'].iloc[-1]
        prev_price = daily_data['Close'].iloc[-2]
        price_change = current_price - prev_price
        price_change_pct = (price_change / prev_price) * 100
        
        print(f"\n💰 기본 정보:")
        print(f"현재가: ${current_price:.2f}")
        print(f"전일 대비: {price_change:+.2f} ({price_change_pct:+.2f}%)")
        print(f"거래량: {daily_data['Volume'].iloc[-1]:,.0f}")
        
        # 거래량 분석
        avg_volume = daily_data['Volume'].rolling(20).mean().iloc[-1]
        volume_ratio = daily_data['Volume'].iloc[-1] / avg_volume
        print(f"평균 거래량 대비: {volume_ratio:.2f}배")
        
        # 기술적 지표
        print(f"\n📈 기술적 지표:")
        
        # 이동평균
        sma_20 = daily_data['Close'].rolling(20).mean().iloc[-1]
        sma_50 = daily_data['Close'].rolling(50).mean().iloc[-1]
        
        print(f"20일 이평선: ${sma_20:.2f} ({((current_price - sma_20) / sma_20 * 100):+.2f}%)")
        print(f"50일 이평선: ${sma_50:.2f} ({((current_price - sma_50) / sma_50 * 100):+.2f}%)")
        
        # RSI
        if rsi_data is not None and not rsi_data.empty:
            current_rsi = rsi_data['RSI'].iloc[-1]
            print(f"RSI: {current_rsi:.1f}", end="")
            if current_rsi > 70:
                print(" (과매수)")
            elif current_rsi < 30:
                print(" (과매도)")
            else:
                print(" (중립)")
        
        # 지지/저항선
        print(f"\n📊 지지/저항선:")
        recent_high = daily_data['High'].rolling(20).max().iloc[-1]
        recent_low = daily_data['Low'].rolling(20).min().iloc[-1]
        
        print(f"20일 최고가: ${recent_high:.2f} ({((recent_high - current_price) / current_price * 100):+.2f}%)")
        print(f"20일 최저가: ${recent_low:.2f} ({((current_price - recent_low) / recent_low * 100):+.2f}%)")
        
        # 매매 신호
        print(f"\n⚡ 매매 신호:")
        signals = []
        
        if current_price > sma_20:
            signals.append("20일 이평선 상향 돌파 → 상승 신호")
        else:
            signals.append("20일 이평선 하향 → 하락 신호")
        
        if volume_ratio > 1.5:
            signals.append("높은 거래량 → 관심 증가")
        
        if rsi_data is not None and not rsi_data.empty:
            current_rsi = rsi_data['RSI'].iloc[-1]
            if current_rsi < 30:
                signals.append("RSI 과매도 → 매수 고려")
            elif current_rsi > 70:
                signals.append("RSI 과매수 → 매도 고려")
        
        for signal in signals:
            print(f"  • {signal}")
        
        print(f"\n{'='*60}")


def setup_alpha_vantage():
    """Alpha Vantage API 설정"""
    print("🔑 Alpha Vantage API 설정")
    print("=" * 50)
    print("1. https://www.alphavantage.co/support/#api-key 에서 무료 API 키 발급")
    print("2. 무료 계정: 분당 5회, 일일 500회 제한")
    print("3. 유료 계정: 더 높은 제한과 실시간 데이터")
    print("=" * 50)
    
    api_key = input("Alpha Vantage API 키를 입력하세요: ").strip()
    
    if not api_key:
        print("❌ API 키가 입력되지 않았습니다.")
        return None
    
    return AlphaVantageAnalyzer(api_key)


def main():
    """메인 함수"""
    print("🚀 Alpha Vantage API 기반 주식 분석 시스템")
    print("=" * 60)
    
    # API 설정
    analyzer = setup_alpha_vantage()
    if analyzer is None:
        return
    
    while True:
        symbol = input("\n분석할 종목 코드 입력 (종료: quit): ").strip().upper()
        
        if symbol.lower() == 'quit':
            print("프로그램을 종료합니다.")
            break
        
        if not symbol:
            print("올바른 종목 코드를 입력해주세요.")
            continue
        
        # 분석 실행
        analyzer.analyze_with_api(symbol)
        
        print("\n" + "="*60)


if __name__ == "__main__":
    main()
